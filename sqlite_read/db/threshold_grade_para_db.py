#!/usr/bin/python3
# coding: utf-8
# date:20241012 threshold_grade_para read write test ok 
import os
import sys
CURRENT_PATH = os.path.dirname(os.path.realpath(__file__))
sys.path.append(os.path.join(CURRENT_PATH, '..'))
from dao.base import BaseDao
from entity import ThresholdGradePara


class GradeParaDao(BaseDao):
    def create(self):
        create_columns = 'id INTEGER PRIMARY KEY AUTOINCREMENT,threshold_grage INTEGER UNIQUE,threshold_para REAL'
        super(GradeParaDao, self).create('threshold_grade_para',create_columns)
        # kw_columns = {'id':'INTEGER PRIMARY KEY AUTOINCREMENT','threshold_grage':'INTEGER UNIQUE','threshold_para':'REAL'}
        # super(GradeParaDao, self).create('threshold_grade_para',**kw_columns)
        print('create success!')
    
    def save(self, args): # 参数=>新增 
        # insert_columns = {'threshold_grage': 1, 'threshold_para': 1.25, 'threshold_grage': 2, 'threshold_para': 1.5,
        #                   'threshold_grage': 3, 'threshold_para': 2, 'threshold_grage': 4, 'threshold_para': 3}
        # insert_columns = 
        print(type(args))
        print(args)
        print(f'0000000000')
        ret = super(GradeParaDao, self).save('threshold_grade_para',**args)


    def query(self, where=None, args=None):
        result = super(GradeParaDao, self).query('threshold_grade_para', 'threshold_grage','threshold_para',
                                        where=where, args=args)
        print(result)
        print('1111111111')
        return result
        # return [
        #     GradePara(item['threshold_grage'], item['threshold_para'])
        #     for item in ret
        # ]
    
    def update(self,sets=None, where=None,args=None):
        # "update users set age = ? where name = ?"
        ret = super(GradeParaDao, self).update('threshold_grade_para',
                                        sets=sets, where=where,args=args)
        print(ret)
        print('22222222')
        return ret

    def delete(self,where=None,args=None):
        # "DELETE FROM users WHERE name = ?"
        ret = super(GradeParaDao, self).delete('threshold_grade_para',
                                        where=where,args=args)
        print(ret)
        print('33333333')


if __name__ == '__main__':
    dao = GradeParaDao() # 初始化,modeltest.sqlite数据库, Connection,不存在则创建
    #---创建数据库---通过---

    print(dao.create()) # 1ok创建数据库表,threshold_grade_para 通过 kw_columns
    #2:插入保存数据;ok
    args = {'threshold_grage':1,'threshold_para':1.25}
    dao.save(args)
    args = {'threshold_grage':2,'threshold_para':1.5}
    dao.save(args)
    args = {'threshold_grage':3,'threshold_para':2}
    dao.save(args)
    args = {'threshold_grage':4,'threshold_para':3.11}
    dao.save(args)

    #--3查询数据:-
    result = dao.query(where=' where threshold_grage=?', args=(1,))
    # ret = dao.query(where=' where threshold_grage=? and threshold_para=? ', args=(1,1.25))
    print(f'result = {result}') # [{'threshold_grage': 1}]
    #--4更改数据:update
    ret = dao.update(sets = ' set threshold_para=?',where=' where threshold_grage=?', args=(2.1,3))
    # ret = dao.update(sets = ' set threshold_para=?',where=' where threshold_grage=?', args=(3.1,4))
    print(f'ret={ret}')
    #--5删除数据:delete
    # ret = dao.delete(where=' where threshold_grage=?', args=(4,))


    print('ok')