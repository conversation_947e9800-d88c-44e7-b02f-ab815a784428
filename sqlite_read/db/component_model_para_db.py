# coding: utf-8
#
import os
import sys
import ast
CURRENT_PATH = os.path.dirname(os.path.realpath(__file__))
sys.path.append(os.path.join(CURRENT_PATH, '..'))
from dao.base import BaseDao
from entity import ComponentModelPara


class ComponentModelParaDao(BaseDao):
    def create(self):
        create_columns = 'id INTEGER PRIMARY KEY AUTOINCREMENT,\
        industry TEXT, component_type TEXT,power_range TEXT,measurement_definition_name TEXT,model_fault_ratio TEXT,model_fault_set_para TEXT,\
        CONSTRAINT uc_ind_comonent_power_meas UNIQUE (industry, component_type,power_range,measurement_definition_name)'
        super(ComponentModelParaDao, self).create('component_model_para',create_columns)
        print('ComponentModelParaDao create success!')
    
    def save(self, args): 
        print(type(args))
        print(args)
        print(f'ComponentModelParaDao save')
        ret = super(ComponentModelParaDao, self).save('component_model_para',**args)

    def query(self, where=None, args=None):
        result = super(ComponentModelParaDao, self).query('component_model_para', 'model_fault_ratio','model_fault_set_para',
                                        where=where, args=args)
        print(result)
        print('ComponentModelParaDao query')
        return result
    
    def update(self,sets=None, where=None,args=None):
        ret = super(ComponentModelParaDao, self).update('component_model_para',
                                        sets=sets, where=where,args=args)
        print(ret)
        print('ComponentModelParaDao update')
        return ret

    def delete(self,where=None,args=None):
        ret = super(ComponentModelParaDao, self).delete('component_model_para',
                                        where=where,args=args)
        print(ret)
        print('ComponentModelParaDao delete')


if __name__ == '__main__':
    dao = ComponentModelParaDao() # 初始化,modeltest.sqlite数据库, Connection,不存在则创建
    #1创建数据库表--component_model_para
    print(dao.create()) # ok

    #2:插入保存数据;ok
    # industry,component_type,power_range,measurement_definition_name,model_fault_ratio,model_fault_set_para
    args = {'industry':'洗煤厂','component_type':'电机','power_range':'10~75KW','measurement_definition_name':'8K加速度波形(0~2000)',
            'model_fault_ratio':str({'unbalance':0.3,'misalignment':0.3,'loose':0.3,'bearing_BPFI':0.5}),'model_fault_set_para':str({'loose_structure_lines':4,'BPFI_structure_lines':4})}
    args = {'industry':'洗煤厂','component_type':'减速机','power_range':'10~75KW','measurement_definition_name':'8K加速度波形(0~2000)',
            'model_fault_ratio':str({'unbalance':0.3,'misalignment':0.3,'loose':0.3,'bearing_BPFI':0.5}),'model_fault_set_para':str({'loose_structure_lines':4,'BPFI_structure_lines':4})}
    args = {'industry':'洗煤厂','component_type':'水泵','power_range':'10~75KW','measurement_definition_name':'8K加速度波形(0~2000)',
            'model_fault_ratio':str({'unbalance':0.3,'misalignment':0.3,'loose':0.3,'bearing_BPFI':0.5}),'model_fault_set_para':str({'loose_structure_lines':4,'BPFI_structure_lines':4})}
    args = {'industry':'洗煤厂','component_type':'风机','power_range':'10~75KW','measurement_definition_name':'8K加速度波形(0~2000)',
            'model_fault_ratio':str({'unbalance':0.3,'misalignment':0.3,'loose':0.3,'bearing_BPFI':0.5}),'model_fault_set_para':str({'loose_structure_lines':4,'BPFI_structure_lines':4})}
    args = {'industry':'洗煤厂','component_type':'滚筒','power_range':'10~75KW','measurement_definition_name':'8K加速度波形(0~2000)',
            'model_fault_ratio':str({'unbalance':0.3,'misalignment':0.3,'loose':0.3,'bearing_BPFI':0.5}),'model_fault_set_para':str({'loose_structure_lines':4,'BPFI_structure_lines':4})}
    args = {'industry':'洗煤厂','component_type':'激振器','power_range':'10~75KW','measurement_definition_name':'8K加速度波形(0~2000)',
            'model_fault_ratio':str({'unbalance':0.3,'misalignment':0.3,'loose':0.3,'bearing_BPFI':0.5}),'model_fault_set_para':str({'loose_structure_lines':4,'BPFI_structure_lines':4})}
#     dao.save(args)

    #--3查询数据:-
    result = dao.query(where=' where industry=? and component_type=? ', args=('洗煤厂','电机'))
    result = dao.query(where=' where industry=? and component_type=? and power_range=?', args=('洗煤厂','电机','10~75KW'))
    result = dao.query(where=' where industry=? and component_type=? and power_range=? and measurement_definition_name=?', args=('洗煤厂','电机','10~75KW','8K加速度波形(0~2000)'))
    print(f'len(result) = {len(result)}') # len = 1
    print(result[0]['model_fault_ratio'])
    print(type(result[0]['model_fault_ratio'])) #result[0]['model_fault_set_para']
    mydict = ast.literal_eval(result[0]['model_fault_ratio'])
    print(type(mydict))
    #--4更改数据:update
    dict_edit = {'unbalance':0.31,'misalignment':0.31,'loose':0.31,'bearing_BPFI':0.51}
    ret = dao.update(sets = ' set model_fault_ratio=?',where=' where component_type=?', args=(str(dict_edit),'电机'))
    #
    print(f'ret={ret}')
    #--5删除数据:delete
    # ret = dao.delete(where=' where industry=? and component_type=? ', args=('洗煤厂','电机'))


    print('ok')
