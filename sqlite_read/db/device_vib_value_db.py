#!/usr/bin/python3
# coding: utf-8
# date:20241012 # DeviceVibValue read write test ok
import os
import sys
import ast
CURRENT_PATH = os.path.dirname(os.path.realpath(__file__))
sys.path.append(os.path.join(CURRENT_PATH, '..'))
from dao.base import BaseDao
from entity import DeviceVibValue


class DeviceVibValueDao(BaseDao):
    def create(self):
        # industry, device_type,device_number,power_range,measurement_definition_name,component_vibration_value
        # CONSTRAINT uc_name_email UNIQUE (name, email)
        create_columns = 'id INTEGER PRIMARY KEY AUTOINCREMENT,\
        industry TEXT, device_type TEXT,device_number TEXT,power_range TEXT,measurement_definition_name TEXT,component_vibration_value TEXT,\
        CONSTRAINT uc_industry_device_power_meas UNIQUE (industry, device_type,device_number,power_range,measurement_definition_name)'
        super(DeviceVibValueDao, self).create('device_vib_value',create_columns)
        print('create success!')
    
    def save(self, args): # 参数=>新增 
        # insert_columns = [{'threshold_grage': 1, 'threshold_para': 1.25}, {'threshold_grage': 2, 'threshold_para': 1.5},
        #                   'threshold_grage': 3, 'threshold_para': 2, 'threshold_grage': 4, 'threshold_para': 3]
        # insert_columns = 
        print(type(args))
        print(args)
        print(f'0000000000')
        ret = super(DeviceVibValueDao, self).save('device_vib_value',**args)


    def query(self, where=None, args=None):
        result = super(DeviceVibValueDao, self).query('device_vib_value', 'component_vibration_value',
                                        where=where, args=args)
        print(result)
        print('1111111111')
        return result
        # return [
        #     GradePara(item['threshold_grage'], item['threshold_para'])
        #     for item in ret
        # ]
    
    def update(self,sets=None, where=None,args=None):
        # "update users set age = ? where name = ?"
        ret = super(DeviceVibValueDao, self).update('device_vib_value',
                                        sets=sets, where=where,args=args)
        print(ret)
        print('22222222')
        return ret

    def delete(self,where=None,args=None):
        # "DELETE FROM users WHERE name = ?"
        ret = super(DeviceVibValueDao, self).delete('device_vib_value',
                                        where=where,args=args)
        print(ret)
        print('33333333')


if __name__ == '__main__':
    dao = DeviceVibValueDao() # 初始化,modeltest.sqlite数据库, Connection,不存在则创建
    #---创建数据库---通过---

    print(dao.create()) # 1ok创建数据库表,threshold_grade_para 通过 kw_columns
    #2:插入保存数据;ok
    # args = {'threshold_grage':1,'threshold_para':1.25}
    # dao.save(args)
    # args = {'threshold_grage':2,'threshold_para':1.5}
    # dao.save(args)
    # args = {'threshold_grage':3,'threshold_para':2}
    # dao.save(args)
    # industry, device_type,device_number,power_range,measurement_definition_name,component_vibration_value
    # args = {'threshold_grage':4,'threshold_para':3.01}
    args = {'industry':'洗煤厂','device_type':'振动筛','device_number':'通用','power_range':'10~75KW','measurement_definition_name':'8K加速度波形(0~2000)',
            'component_vibration_value':str({'激振器':35})}
    # args = {'industry':'洗煤厂','device_type':'水泵','device_number':'通用','power_range':'10~75KW','measurement_definition_name':'8K加速度波形(0~2000)',
    #         'component_vibration_value':str({'电机':2,'水泵':2})}
    
    # args = {'industry':'洗煤厂','device_type':'离心机','device_number':'通用','power_range':'75~100KW','measurement_definition_name':'8K加速度波形(0~2000)',
    #         'component_vibration_value':str({'电机':2,'离心机':2})}
    # dao.save(args)

    #--3查询数据:-
    result = dao.query(where=' where industry=? and device_type=? ', args=('洗煤厂','振动筛'))
    # ret = dao.query(where=' where threshold_grage=? and threshold_para=? ', args=(1,1.25))
    print(f'result = {result}') # [{'threshold_grage': 1}]
    print(result[0]['component_vibration_value'])
    print(type(result[0]['component_vibration_value']))
    mydict = ast.literal_eval(result[0]['component_vibration_value'])
    print(type(mydict))
    #--4更改数据:update
    dict_edit = {'激振器':35.12}
    ret = dao.update(sets = ' set component_vibration_value=?',where=' where device_type=?', args=(str(dict_edit),'振动筛'))
    # ret = dao.update(sets = ' set threshold_para=?',where=' where threshold_grage=?', args=(3.1,4))
    print(f'ret={ret}')
    #--5删除数据:delete
    # ret = dao.delete(where=' where industry=? and device_type=? ', args=('洗煤厂','振动筛'))


    print('ok')
