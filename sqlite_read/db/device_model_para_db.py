#!/usr/bin/python3
# coding: utf-8
import os
import sys
import ast
CURRENT_PATH = os.path.dirname(os.path.realpath(__file__))
sys.path.append(os.path.join(CURRENT_PATH, '..'))
from dao.base import BaseDao
from entity import DeviceModelPara


class DeviceModelParaDao(BaseDao):
    def create(self):
        create_columns = 'id INTEGER PRIMARY KEY AUTOINCREMENT,\
        industry TEXT, device_type TEXT,device_number TEXT, component_type TEXT,power_range TEXT,measurement_definition_name TEXT,model_fault_ratio TEXT,model_fault_set_para TEXT,\
        CONSTRAINT uc_ind_dev_num_comonent_power_meas UNIQUE (industry,device_type,device_number, component_type,power_range,measurement_definition_name)'
        super(DeviceModelParaDao, self).create('device_model_para',create_columns)
        print('DeviceModelParaDao create success!')
    
    def save(self, args): # 参数=>新增
        print(type(args))
        print(args)
        print(f'DeviceModelParaDao save')
        ret = super(DeviceModelParaDao, self).save('device_model_para',**args)

    def query(self, where=None, args=None):
        result = super(DeviceModelParaDao, self).query('device_model_para', 'model_fault_ratio','model_fault_set_para',
                                        where=where, args=args)
        print(result)
        print('DeviceModelParaDao query')
        return result
    
    def update(self,sets=None, where=None,args=None):
        #
        ret = super(DeviceModelParaDao, self).update('device_model_para',
                                        sets=sets, where=where,args=args)
        print(ret)
        print('DeviceModelParaDao update')
        return ret

    def delete(self,where=None,args=None):
        #
        ret = super(DeviceModelParaDao, self).delete('device_model_para',
                                        where=where,args=args)
        print(ret)
        print('DeviceModelParaDao delete')


if __name__ == '__main__':
    dao = DeviceModelParaDao() # 初始化,modeltest.sqlite数据库, Connection,不存在则创建

    print(dao.create()) # 1ok创建数据库表,
    #2:插入保存数据;ok
    # industry,component_type,power_range,measurement_definition_name,model_fault_ratio,model_fault_set_para
    # args = {'industry':'洗煤厂','device_type':'振动筛','device_number':'通用','component_type':'电机','power_range':'10~75KW','measurement_definition_name':'8K加速度波形(0~2000)',
    #         'model_fault_ratio':str({'unbalance':0.3,'misalignment':0.3,'loose':0.3,'bearing_BPFI':0.5}),'model_fault_set_para':str({'loose_structure_lines':4,'BPFI_structure_lines':4})}
    args = {'industry':'洗煤厂','device_type':'离心机','device_number':'通用','component_type':'电机','power_range':'10~75KW','measurement_definition_name':'8K加速度波形(0~2000)',
            'model_fault_ratio':str({'unbalance':0.3,'misalignment':0.3,'loose':0.3,'bearing_BPFI':0.5}),'model_fault_set_para':str({'loose_structure_lines':4,'BPFI_structure_lines':4})}

    # dao.save(args)

    #--3查询数据:-dao.query()
    result = dao.query(where=' where industry=? and device_type=? and component_type=? ', args=('洗煤厂','振动筛','电机'))
    result = dao.query(where=' where industry=? and device_type=? and device_number=? and component_type=? and power_range=? and measurement_definition_name=?', args=('洗煤厂','振动筛','通用','电机','10~75KW','8K加速度波形(0~2000)'))
    print(f'result = {result}') # [{'threshold_grage': 1}]
    print(result[0]['model_fault_ratio'])
    print(type(result[0]['model_fault_ratio']))
    mydict = ast.literal_eval(result[0]['model_fault_ratio']) #model_fault_set_para
    print(type(mydict))
    #--4更改数据:update
    # dict_edit = {'unbalance':0.32,'misalignment':0.32,'loose':0.32,'bearing_BPFI':0.52}
    # ret = dao.update(sets = ' set model_fault_ratio=?',where=' where industry=? and device_type=? and component_type=?', args=(str(dict_edit),'洗煤厂','振动筛','电机'))
    dict_editpara = {'low_freq_ratio':0.32,'high_freq_ratio':0.32,'loose':0.32,'bearing_BPFI':0.52}
    ret = dao.update(sets = ' set model_fault_set_para=?',where=' where industry=? and device_type=? and component_type=?', args=(str(dict_editpara),'洗煤厂','振动筛','激振器'))

    #
    print(f'ret={ret}')
    #--5删除数据:delete
    # ret = dao.delete(where=' where industry=? and component_type=? ', args=('洗煤厂','电机'))


    print('ok')
