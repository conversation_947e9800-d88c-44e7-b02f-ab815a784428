# coding: utf-8
from . import Connection


class BaseDao:
    def __init__(self):
        self.conn = Connection()

    def query(self, table_name, *columns, where=None, args=None):
        sql = 'select %s from %s '
        sql = sql % ( ','.join(columns) , table_name)
        if where:
            sql += where
        print(f'sql = {sql}')
        with self.conn as c:
            if args:
                c.execute(sql, args)
            else:
                c.execute(sql)

            ret = c.fetchall()
            # print(f'len(ret)={len(ret)}')
            # print(type(ret[0]))

        return ret

    def create(self, table_name, kw):
        print(f'创建数据库表table_name={table_name}:kw={kw},type(kw)={type(kw)}')
        sql = 'create table IF NOT EXISTS %s  (%s) '
        sql = sql % (table_name,kw)
        print(f'sql = {sql}') 
        with self.conn as c:
            c.execute(sql)
        return
    
    # 插入 保存
    def save(self, table_name,**args):
        print(f'type(args)={type(args)},args={args}')
        # 根据字典的键和值动态生成SQL语句
        sql = 'INSERT INTO %s (' + ', '.join(args.keys()) + ') VALUES (' + ', '.join(['?'] * len(args.values())) + ')'
        sql = sql % table_name
        print(f'sql={sql}')
        with self.conn as c:
            c.execute(sql, tuple(args.values()))
            # 判断更新操作是否操作成功;
            if c.rowcount == 0:
                print(f'---save failed!---')
                flag = -1
            else:
                print('---save success!---')
                flag = 1            
        return flag

    def update(self, table_name, sets=None, where=None,args = None):
        #"update users set age = ? where name = ?"
        print(f'table_name = {table_name}, sets={sets},where={where},args={args}')
        sql = 'update %s  %s  %s'
        sql = sql % ( table_name,sets,where)
        print(f'sql={sql}')
        # values = []
        with self.conn as c:
            c.execute(sql, args)
            # 判断更新操作是否操作成功;
            if c.rowcount == 0:
                print(f'---Update failed!---')
                flag = -1
            else:
                print('---Update success!---')
                flag = 1
        return flag

    def delete(self, table_name, where=None, args=None):
        print(f'table_name = {table_name},where={where},args={args}')
        sql = 'DELETE FROM %s  %s '
        sql = sql % ( table_name,where)
        print(f'sql={sql}')
        with self.conn as c:
            c.execute(sql, args)
        return    
