# coding: utf-8
import sqlite3
import os
import sys
from config.config_db import load_modeltest_config_db_path
from config.config_env import env_name  # 导入环境名称变量

# 从配置文件加载数据库配置
modeltest_db_path = load_modeltest_config_db_path(env_name)

#
# CURRENT_PATH = os.path.dirname(os.path.realpath(__file__))
# print(f'sqlite3 file path is -->>>>>>-----: {CURRENT_PATH}')
# sqlite_fiename = os.path.join(CURRENT_PATH.rsplit('\\',1)[0], 'modeltest.sqlite')
# # sqlite_fiename = os.path.join(CURRENT_PATH.rsplit('\\',1)[0], 'modeltest2.sqlite')
# print(f'拼接后的地址:sqlite_fiename={sqlite_fiename}')

class Connection():
    def __init__(self):
        self.conn = sqlite3.connect(modeltest_db_path) # 不存在则创建
        
    def dict_factory(self,cursor, row):  
        d = {}  
        for idx, col in enumerate(cursor.description):  
            d[col[0]] = row[idx]  
        return d

    def __enter__(self):
        self.conn.row_factory = self.dict_factory
        return self.conn.cursor()

    def __exit__(self, exc_type, exc_val, exc_tb):
        if exc_type:
            self.conn.rollback()  # 回滚事务
            # 日志收集异常信息， 上报给服务器
            print(f'exc_type={exc_type}')
        else:
            self.conn.commit()

    def close(self):
        try:
            self.conn.close()
        except:
            pass