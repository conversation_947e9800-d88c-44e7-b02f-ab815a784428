#!/usr/bin/python3
# coding: utf-8


class ThresholdGradePara:
    def __init__(self, threshold_grage, threshold_para):
        self.threshold_grage = threshold_grage
        self.threshold_para = threshold_para

    def __str__(self):
        return "%s->%s(%s) " % (self.threshold_grage, self.threshold_para)

    def __repr__(self):
        return self.__str__()


class DeviceVibValue:
    def __init__(self, industry, device_type,device_number,power_range,measurement_definition_name,component_vibration_value):
        self.industry = industry
        self.device_type = device_type
        self.device_number = device_number
        self.power_range = power_range
        self.measurement_definition_name = measurement_definition_name
        self.component_vibration_value = component_vibration_value


class ComponentModelPara:
    def __init__(self, industry,component_type,power_range,measurement_definition_name,model_fault_ratio,model_fault_set_para):
        self.industry = industry
        self.component_type = component_type
        self.power_range = power_range
        self.measurement_definition_name = measurement_definition_name
        self.model_fault_ratio = model_fault_ratio
        self.model_fault_set_para = model_fault_set_para


class DeviceModelPara:
    def __init__(self, industry,device_type,device_number,component_type,power_range,measurement_definition_name,model_fault_ratio,model_fault_set_para):
        self.industry = industry
        self.device_type = device_type
        self.device_number = device_number
        self.component_type = component_type
        self.power_range = power_range
        self.measurement_definition_name = measurement_definition_name
        self.model_fault_ratio = model_fault_ratio
        self.model_fault_set_para = model_fault_set_para