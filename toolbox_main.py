'''
feature_extr: 特征值计算;
transform_graph: 谱图计算
plot_wave:测试绘图
sqlite_read: 配置参数读取
'''
import numpy as np
import pandas as pd
import bisect
import ast
from utils.time_feature import time_feature
from utils.scipy_fft import fft_plot,envelope_plot
# from utils.scipy_envelope import envelope_plot
from utils.electricity_feature import electricity_feature
from utils.scipy_fft import fft_plot,envelope_plot,receps,jifen
from mechanism.fault_detect_location import fault_detect_location
from sqlite_read.db.device_model_para_db import DeviceModelParaDao
from sqlite_read.db.component_model_para_db import ComponentModelParaDao
from sqlite_read.db.threshold_grade_para_db import GradeParaDao
from log.logger import LOG
import matplotlib
matplotlib.use('TkAgg')
import matplotlib.pyplot as plt


def plot_wave(wave,fs):
    plt.figure()
    plt.subplot(311)
    plt.plot(wave[10:-10])
    plt.title(f'fs:{fs},t={len(wave)/fs}s')
    norm_x = wave - np.mean(wave)
    freq_x, real_amp,_a = fft_plot(norm_x,fs)
    plt.subplot(312)
    plt.plot(freq_x,real_amp)
    pinyu_data = pd.DataFrame({'freq_x':freq_x,'real_amp':real_amp})
    freq_x2, real_amp2 = envelope_plot(norm_x,fs)
    plt.subplot(313)
    baol_data = pd.DataFrame({'freq_x2':freq_x2,'envelope_amp':real_amp2})
    plt.plot(freq_x2,real_amp2)
    plt.show()


def feature_extr(wave_type,fs,N,*wave_array):
    print(f'wave_type={wave_type}')
    if wave_type == 'acc' or wave_type =='vel':
        result_feature = time_feature(*wave_array)
        print(result_feature)
        return result_feature
    elif wave_type == 'stress':
        print(' stress-pass')
    elif wave_type == 'circuit':
        print(' circuit')
        norm_x,norm_y,norm_z = wave_array
        rms_x,rms_y,rms_z,fengzhi_x,fengzhi_y,fengzhi_z,CF_x,CF_y,CF_z,unbalance_degree,max_f1,max_f2,max_f3,THD_A,THD_B,THD_C = electricity_feature(norm_x,norm_y,norm_z,fs)
        return rms_x,rms_y,rms_z,fengzhi_x,fengzhi_y,fengzhi_z,CF_x,CF_y,CF_z,unbalance_degree,max_f1,max_f2,max_f3,THD_A,THD_B,THD_C
    else :
        print(' not support!')
    

def transform_graph(wave_array,fs,N,graph_type):
    if graph_type == 'spectrum':
        freq_x, real_amp,real_angle = fft_plot(wave_array, fs)
        return freq_x,real_amp
    elif graph_type == 'envelope':
        freq_x, real_amp = envelope_plot(wave_array, fs)
        return freq_x,real_amp
    elif graph_type == 'cepstrum':
        time_x,ceps = receps(wave_array,fs)
        return time_x,ceps
    elif graph_type == 'integral':
        fmin = 10
        fmax = fs/2.56
        c = 1000
        it = 1
        y_wave = jifen(wave_array, fs, fmin, fmax,c, it)
        return y_wave


def diagnosis_fault(device_type,component_type, wave_array, wave_type, fs, speed, power, bearing_parameter, gearing_parameter, fan_number, threshold_alarm_level, model_ratio, model_fault_ratio,model_fault_set_para,threshold_grade_para):
    LOG.info(f'device_type={device_type},component_type={component_type},wave_type={wave_type},fs={fs},speed = {speed},power={power}')
    LOG.info(f'bearing_parameter={bearing_parameter},gearing_parameter={gearing_parameter},fan_number={fan_number},threshold_alarm_level={threshold_alarm_level}')
    LOG.info(f'model_ratio={model_ratio},model_fault_ratio={model_fault_ratio},model_fault_set_para={model_fault_set_para},threshold_grade_para={threshold_grade_para}')
    fault_type, fault_grade, fault_index,vib_lub,vib_lub_level,fault_grade_list,dict_features = fault_detect_location(
        component_type, wave_array, wave_type, fs, speed, power, bearing_parameter, gearing_parameter, fan_number, threshold_alarm_level, model_ratio, model_fault_ratio,model_fault_set_para,threshold_grade_para)
    return fault_type, fault_grade, fault_index,vib_lub,vib_lub_level,fault_grade_list,dict_features


def toolbox_main(industry,source,model_list,device_name,device_type,device_number,device_power,device_speed,component_name,component_type,bearing_para,gearing_para,fan_number,component_speed,measurement_definition_name,fs,N,collect_time,wave_type,path_wave):
    LOG.info(f'source={source},model_list={model_list},device_name={device_name},device_type={device_type},device_power={device_power},device_speed={device_speed})')
    LOG.info(f'component_type={component_type},bearing_para={bearing_para},gearing_para={bearing_para},fan_number={fan_number},component_speed={component_speed},measurement_definition_name={component_speed},fs={fs},N={N},collect_time={collect_time},wave_type={wave_type}')
    power_list = [10,75]
    power_index_name = ['0~10KW','10~75KW','>75KW']
    power_index = bisect.bisect_left(power_list, device_power) # 0 1 2
    power_range_name = power_index_name[power_index]
    print(f'power_range_name={power_range_name}')
    result = []
    result2 = []
    result3 = []
    Devicedao = DeviceModelParaDao()
    Devicedao.create()
    result = Devicedao.query(where=' where industry=? and device_type=? and device_number=? and component_type=? and power_range=? and measurement_definition_name=?', 
                             args=(industry,device_type,device_number,component_type,power_range_name,measurement_definition_name))
    print(f'result = {result}') # [{'threshold_grage': 1}]
    if len(result):
        print(result[0]['model_fault_ratio'])
        print(type(result[0]['model_fault_ratio']))
        Device_model_fault_ratio = ast.literal_eval(result[0]['model_fault_ratio']) #model_fault_set_para
        Device_model_fault_set_para = ast.literal_eval(result[0]['model_fault_set_para'])
        print(f'Devicedao model_fault_ratio ={Device_model_fault_ratio}')
        print(f'Devicedao model_fault_ratio ={Device_model_fault_set_para}')
    else:
        print(f'empty:!!!')
        Device_model_fault_ratio = {}
        Device_model_fault_set_para = {}
    if len(Device_model_fault_ratio) == 0:
        print(f'未匹配到设备型号')
        Componentdao = ComponentModelParaDao()
        Componentdao.create()
        result2 = Componentdao.query(where=' where industry=? and component_type=? and power_range=? and measurement_definition_name=?', 
                                    args=(industry,component_type,power_range_name,measurement_definition_name))
        if len(result2):
            Component_model_fault_ratio = ast.literal_eval(result2[0]['model_fault_ratio']) #model_fault_set_para
            Component_model_fault_set_para = ast.literal_eval(result2[0]['model_fault_set_para'])
            print(f'Componentdao model_fault_ratio ={Component_model_fault_ratio}')
            print(f'Componentdao model_fault_ratio ={Component_model_fault_set_para}')
            print(f'Componentdao len(result) = {len(result2)}') # len = 1
        else:
            Component_model_fault_ratio = {}
            Component_model_fault_set_para = {}
    
    if len(result)==0 and len(result2)==0:
        LOG.info(f'未查询到模型配置信息-return None')
        model_fault_ratio = {}
        model_fault_set_para = {}
    elif len(result):
        model_fault_ratio = Device_model_fault_ratio
        model_fault_set_para = Device_model_fault_set_para
    elif len(result2):
        model_fault_ratio = Component_model_fault_ratio
        model_fault_set_para = Component_model_fault_set_para
    LOG.info(f'len(result)={len(result)},len(result2)={len(result2)},model_fault_ratio={model_fault_ratio},model_fault_set_para={model_fault_set_para}')
    GradeDao = GradeParaDao() 
    GradeDao.create()
    result3 = GradeDao.query()
    threshold_grade_para = {}
    for ri in result3:
        print(ri)
        vv = []
        for k,v in ri.items():
            print(f'k={k},v={v}')
            vv.append(v)
        threshold_grade_para[vv[0]]=vv[1]
    print(f'threshold_grade_para={threshold_grade_para}')
    # threshold_grade_para = {1:1.25,2:1.5,3:2,4:3}
    threshold_alarm_level = 0 #
    model_ratio = 1 #机理模型比例
    if component_speed is None or component_speed == 0 or component_speed == [] :
        component_speed = device_speed
    #---读取path_wave路径下的波形文件数据---
    norm_x = np.loadtxt(path_wave)
    wave_array = norm_x - np.mean(norm_x)
    wave_array = wave_array.tolist()
    print(f'type wave_array: {type(wave_array)}')
    rms = np.sqrt(np.sum(np.power(wave_array, 2)/N))
    print(f'波形rms:{rms}')
    fault_type, fault_grade, fault_index,vib_lub,vib_lub_level,fault_grade_list,dict_features = diagnosis_fault(
        device_type,component_type, wave_array, wave_type, fs, component_speed, device_power, bearing_para, gearing_para, fan_number, threshold_alarm_level, model_ratio, model_fault_ratio,model_fault_set_para,threshold_grade_para)
    print(f'fault_type={fault_type},fault_grade={fault_grade},vib_lub={vib_lub},vib_lub_level={vib_lub_level},fault_grade_list={fault_grade_list}')
    print(f'dict_features={dict_features}')
    LOG.info(f'fault_type={fault_type},fault_grade={fault_grade}')
    LOG.info(f'model_list={model_list}')
    fault_type_real = []
    fault_grade_list_real = []
    for i,f in enumerate(fault_type):
        print(f'i = {i},f = {f}')
        if f in model_list:
            fault_type_real.append(f)
            fault_grade_list_real.append(fault_grade_list[i])
    if len(fault_grade_list_real) == 0:
        fault_grade_real = 0
    else:
        fault_grade_real = np.max(fault_grade_list_real)
    LOG.info(f'fault_type_real={fault_type_real},fault_grade_list_real={fault_grade_list_real}\n')
    return fault_type_real,fault_grade_list_real,dict_features


if __name__ == '__main__':
    print(f'===========================\n')
    print(f'===========================\n')
    # path_wave = r'D:\文档\云科\机理模型平台\temp\1693535255379779585_1790665164398997506_2024-06-22-10-58-24.csv'
    path_wave = r'G:\数据验证\入选皮带电机-轴承误报-导出数据\1693534535838539777_1790666557029560321\1693534535838539777_1790666557029560321_2024-07-26-04-51-40.csv'
    industry = '洗煤厂'
    source = '新安洗煤厂'
    model_list = [1,2,3,4,5,6,7,8]
    device_name ='327离心机'
    device_type = '离心机' #待定义int:1=合介泵,2=离心机,3=振动筛,...
    device_number='通用' # LX001
    device_power = 20
    device_speed = 1500
    component_name ='电机驱动端水平' # 减速机一轴输入端
    component_type = '电机' # 已定义:1=电机，2=风机，3=减速机，4=水泵，5=滚筒，6=激振器；7=
    bearing_para = [3.1, 4.1, 2.1, 0.5, 3.1, 4.1, 2.1, 0.5] # ---Sidas部位绑定的信息
    gearing_para = {'减速机一轴': [100.3, 123.3],'减速机二轴': [80.3, 88.3]} #---Sidas部位绑定的信息,[转速rpm,啮合频率]
    fan_number = [3] #---Sidas部位绑定的信息3 or 6
    component_speed = 1500 #存在优先使用
    measurement_definition_name = '8K加速度波形(0~2000)'
    fs = 5120
    N = 8192
    collect_time = '2024-06-22-10-58-24'
    wave_type = 1 #1: 加速度,速度,温度，应力波
    # wave_data = norm_x #波形数据
    norm_x = np.loadtxt(path_wave)
    plot_wave(norm_x,fs)
    fault_type_real,fault_grade_list_real,dict_features = toolbox_main(industry,source,model_list,device_name,device_type,device_number,device_power,device_speed,component_name,component_type,bearing_para,gearing_para,fan_number,component_speed,measurement_definition_name,fs,N,collect_time,wave_type,path_wave)
    print(f'fault_type_real={fault_type_real},fault_grade_list_real={fault_grade_list_real},dict_features={dict_features}')

    # path_wave = r'D:\文档\云科\机理模型平台\temp\1693535255379779585_1790665164398997506_2024-06-22-10-58-24.csv'
    # fs = 5120
    # N = 8192
    # norm_x = np.loadtxt(path_wave)
    # wave_type = 'acc'  #acc,vel,stress,circuit
    # plot_wave(norm_x,fs)
    # #1:特征计算
    # result_feature = feature_extr(wave_type,fs,N,norm_x)
    # print('-1-')
    # wave_type = 'circuit'  #acc,vel,stress,circuit
    # result_feature = feature_extr(wave_type,fs,N,norm_x,norm_x,norm_x) # 三相电流
    # print('-2-')
    # #2:谱图类型
    # graph_type = 'spectrum'  #spectrum,envelope,cepstrum,integral
    # result_graph = transform_graph(norm_x,fs,N,graph_type)
    # print(len(result_graph))

    # #3:(1)机理调用----rabbitmq数据接入----
    # industry = '洗煤厂'
    # source = '新安洗煤厂'
    # model_list = [1,2,3,4,5,6,7,8]
    # device_name ='327离心机'
    # device_type = '离心机' #待定义int:1=合介泵,2=离心机,3=振动筛,...
    # device_number='通用' # LX001
    # device_power = 20
    # device_speed = 1500
    # component_name ='电机驱动端水平' # 减速机一轴输入端
    # component_type = '电机' # 已定义:1=电机，2=风机，3=减速机，4=水泵，5=滚筒，6=激振器；7=
    # bearing_para = [3.1, 4.1, 2.1, 0.5, 3.1, 4.1, 2.1, 0.5] # ---Sidas部位绑定的信息
    # gearing_para = {'减速机一轴': [100.3, 123.3],'减速机二轴': [80.3, 88.3]} #---Sidas部位绑定的信息,[转速rpm,啮合频率]
    # fan_number = [3] #---Sidas部位绑定的信息3 or 6
    # component_speed = 1500 #存在优先使用
    # measurement_definition_name = '8K加速度波形(0~2000)'
    # fs = fs
    # N = N
    # collect_time = '2024-06-22-10-58-24'
    # wave_type = 1 #1: 加速度,速度,温度，应力波
    # wave_data = norm_x #波形数据

    # #(2)根据mqtt传入的信息，查询库表，确定调用设备机理模型或部位机理模型的字典,
    # #---行业 -----设备类型-------设备型号------部件类型------功率范围-------测量定义--
    # power_list = [10,75]
    # power_index_name = ['0~10KW','10~75KW','>75KW']
    # power_index = bisect.bisect_left(power_list, device_power) # 0 1 2
    # power_range_name = power_index_name[power_index]
    # print(f'power_range_name={power_range_name}')
    # #  industry  device_type  device_number component_type device_power  measurement_definition_name
    # result = []
    # result2 = []
    # result3 = []
    # Devicedao = DeviceModelParaDao()
    # Devicedao.create()
    # result = Devicedao.query(where=' where industry=? and device_type=? and device_number=? and component_type=? and power_range=? and measurement_definition_name=?', 
    #                          args=(industry,device_type,device_number,component_type,power_range_name,measurement_definition_name))
    # print(f'result = {result}') # [{'threshold_grage': 1}]
    # if len(result):
    #     print(result[0]['model_fault_ratio'])
    #     print(type(result[0]['model_fault_ratio']))
    #     Device_model_fault_ratio = ast.literal_eval(result[0]['model_fault_ratio']) #model_fault_set_para
    #     Device_model_fault_set_para = ast.literal_eval(result[0]['model_fault_set_para'])
    #     print(f'Devicedao model_fault_ratio ={Device_model_fault_ratio}')
    #     print(f'Devicedao model_fault_ratio ={Device_model_fault_set_para}')
    # else:
    #     print(f'empty:!!!')
    #     Device_model_fault_ratio = {}
    #     Device_model_fault_set_para = {}
    # if len(Device_model_fault_ratio) == 0:
    #     print(f'未匹配到设备型号')
    #     Componentdao = ComponentModelParaDao()
    #     Componentdao.create()
    #     result2 = Componentdao.query(where=' where industry=? and component_type=? and power_range=? and measurement_definition_name=?', 
    #                                 args=(industry,component_type,power_range_name,measurement_definition_name))
    #     if len(result2):
    #         Component_model_fault_ratio = ast.literal_eval(result2[0]['model_fault_ratio']) #model_fault_set_para
    #         Component_model_fault_set_para = ast.literal_eval(result2[0]['model_fault_set_para'])
    #         print(f'Componentdao model_fault_ratio ={Component_model_fault_ratio}')
    #         print(f'Componentdao model_fault_ratio ={Component_model_fault_set_para}')
    #         print(f'Componentdao len(result) = {len(result2)}') # len = 1
    #     else:
    #         Component_model_fault_ratio = {}
    #         Component_model_fault_set_para = {}
    
    # if len(result)==0 and len(result2)==0:
    #     LOG.info(f'未查询到模型配置信息-return None')
    # elif len(result):
    #     model_fault_ratio = Device_model_fault_ratio
    #     model_fault_set_para = Device_model_fault_set_para
    # elif len(result2):
    #     model_fault_ratio = Component_model_fault_ratio
    #     model_fault_set_para = Component_model_fault_set_para


    # LOG.info(f'len(result)={len(result)},len(result2)={len(result2)},model_fault_ratio={model_fault_ratio},model_fault_set_para={model_fault_set_para}')

    # model_fault_ratio = {'unbalance':0.000003,'misalignment':0.3,'loose':0.3,'bearing_BPFI':0.5,'bearing_BPFO':0.5,'bearing_BSF':0.5,'bearing_FTF':0.5,\
    #               'gearing':0.5,'fan':0.3,'rub':0.5,'lub':0.5}
    # #模糊诊断---fuzzy_diagnosis
    # model_fault_set_para = {'loose_structure_lines': 6, 'BPFI_structure_lines': 6,'bearing_env':4,'gear_structure_lines':6,'gearing_env':4,'fan_structure_lines':5,'rub_structure_lines':3}
    # model_fault_set_para['threshold_max_f'] =1
    # model_fault_set_para['threshold_side_bands'] = 0.4
    # model_fault_set_para['envelope_fuzzy'] = 200 #Hz
    # model_fault_set_para['envelope_fuzzy_value'] = 1
    # model_fault_set_para['threshold_middle_f4'] = 0.04
    # model_fault_set_para['threshold_high_f4'] =0.55

    # #------------根据行业、设备部件正常振动值--------------
    # component_vibration_value = {'电机': 2, '离心机': 2} #component_type=1;
    # component_vibration_value = 2 # component_type=1,电机,value = 2
    # #------------查询等级标准设置表------------------------
    # GradeDao = GradeParaDao() 
    # GradeDao.create()
    # result3 = GradeDao.query()
    # threshold_grade_para = {}
    # for ri in result3:
    #     print(ri)
    #     vv = []
    #     for k,v in ri.items():
    #         print(f'k={k},v={v}')
    #         vv.append(v)
    #     threshold_grade_para[vv[0]]=vv[1]
    # print(f'threshold_grade_para={threshold_grade_para}')
    # # threshold_grade_para = {1:1.25,2:1.5,3:2,4:3}
    # threshold_alarm_level = 0 #
    # model_ratio = 1 #机理模型比例
    # if component_speed is None or component_speed == 0 or component_speed == [] :
    #     component_speed = device_speed
    # wave_array = norm_x - np.mean(norm_x)
    # wave_array = wave_array.tolist()
    # print(f'type wave_array: {type(wave_array)}')
    # rms = np.sqrt(np.sum(np.power(wave_array, 2)/N))
    # print(f'波形rms:{rms}')
    # fault_type, fault_grade, fault_index,vib_lub,vib_lub_level,fault_grade_list,dict_features = diagnosis_fault(
    #     device_type,component_type, wave_array, wave_type, fs, component_speed, device_power, bearing_para, gearing_para, fan_number, threshold_alarm_level, model_ratio, model_fault_ratio,model_fault_set_para,threshold_grade_para)
    # print(f'fault_type={fault_type},fault_grade={fault_grade},vib_lub={vib_lub},vib_lub_level={vib_lub_level},fault_grade_list={fault_grade_list}')
    # print(f'dict_features={dict_features}')
    # print(f'处理模型列表中的故障类型:model_list={model_list}')
    # fault_type_real = []
    # fault_grade_list_real = []
    # for i,f in enumerate(fault_type):
    #     print(f'i = {i},f = {f}')
    #     if f in model_list:
    #         fault_type_real.append(f)
    #         fault_grade_list_real.append(fault_grade_list[i])
    # fault_grade_real = np.max(fault_grade_list_real)



    
