import logging
import os
import sys
import threading
import yaml
from logging import handlers
from time import sleep

CURRENT_PATH = os.path.dirname(os.path.realpath(__file__))
CONFIG_NAME = 'config.yaml'
"""
config.yaml:
    log_path: ./logs/test.log
    level: debug
    interval: 1
    when: D
    back_count: 30
"""


class Config(object):
    def __init__(self):
        self.__load_config()

    def __load_config(self):
        file_path = os.path.join(CURRENT_PATH, CONFIG_NAME)
        with open(file_path) as f:
            config = yaml.safe_load(f.read())
            if config is None:
                print('Load config failed, path=\"{}\"'.format(file_path))
                sys.exit(1)
        self.log_path = config.get('log_path')
        if self.log_path is None:
            print('Required properties missing, name="log_path"')
            sys.exit(1)
        else:
            self.log_path = self.log_path  #os.path.join(CURRENT_PATH, self.log_path)
        self.level = config.get('level')
        if self.level is None:
            print('Required properties missing, name="level"')
            sys.exit(1)
        self.interval = config.get('interval')
        if self.interval is None:
            print('Required properties missing, name="interval"')
            sys.exit(1)
        self.when = config.get('when')
        if self.when is None:
            print('Required properties missing, name="when"')
            sys.exit(1)
        self.back_count = config.get('back_count')
        if self.back_count is None:
            print('Required properties missing, name="back_count"')
            sys.exit(1)
        return


class Logger(object):
    __log_level = {
        'debug': logging.DEBUG,
        'info': logging.INFO,
        'warning': logging.WARNING,
        'error': logging.ERROR,
        'critical': logging.CRITICAL
    }
    __instance_lock = threading.Lock()

    def __init__(self, config,
                 fmt='[%(asctime)s] (%(module)s:%(funcName)s:%(lineno)s): <%(levelname)s> %(message)s'):
        file_dir, file_name = os.path.split(config.log_path)
        # 创建日志文件夹
        os.makedirs(file_dir, exist_ok=True)
        self.logger = logging.getLogger(config.log_path)
        # 设置日志格式
        formatter = logging.Formatter(fmt)
        # 设置日志级别
        self.logger.setLevel(self.__log_level.get(config.level))
        # 往屏幕上输出
        sh = logging.StreamHandler()
        # 设置屏幕上显示的格式
        sh.setFormatter(formatter)
        self.logger.addHandler(sh)
        # 设置日志输出到文件策略
        """
        backupCount：备份文件的个数，如果超过这个个数，会自动删除
        interval：间隔多少个when自动重建日志
        when：间隔的时间单位，单位有以下几种：
            "S"：Second
            "M"：Minutes
            "H"：Hour
            "D"：Days
            "W"：Week day（0 = Monday）
            "midnight"：Roll over at midnight（每天凌晨）
        """
        th = handlers.TimedRotatingFileHandler(filename=config.log_path,
                                               backupCount=config.back_count,
                                               interval=config.interval,
                                               when=config.when,
                                               encoding='utf-8')
        # 设置文件里写入的格式
        th.setFormatter(formatter)
        self.logger.addHandler(th)

    def __new__(cls, *args, **kwargs):
        if not hasattr(Logger, "_instance"):
            with Logger.__instance_lock:
                if not hasattr(Logger, "_instance"):
                    Logger._instance = object.__new__(cls)
        return Logger._instance


LOG = Logger(Config()).logger

if __name__ == '__main__':
    LOG.debug('debug')
    LOG.info('info')
    LOG.warning('warning')
    LOG.error('error')
    LOG.critical('critical')
    count = 1
    while True:
        LOG.info(count)
        count += 1
        sleep(1)
