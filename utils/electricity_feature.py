'''
date:20240801;
计算电流 1三相电流有效值、峰值;
2三相电流尖峰系数CF
3三相电流平衡性
4三相电流频率
5总谐波失真THD
'''
import numpy as np
from utils.common_use import get_section_freq_max #
from utils.scipy_fft import fft_plot


# get_n_harmonic 1X ~6X for current A B C:
def get_n_harmonic(fs,main_1X_real,real_amp, freq_x, N, deltf):
    main_2X = main_1X_real * 2
    main_3X = main_1X_real * 3
    main_4X = main_1X_real * 4
    main_5X = main_1X_real * 5
    main_6X = main_1X_real * 6   

    main_2X_point_left = int((main_2X - 3 * deltf)  * N / fs)
    main_2X_point_right = int((main_2X + 3 * deltf)  * N / fs)
    main_2X, max_fvalues_main2X,_a =  get_section_freq_max(real_amp, freq_x, main_2X_point_left, main_2X_point_right)
    main_3X_point_left = int((main_3X - 3 * deltf)  * N / fs)
    main_3X_point_right = int((main_3X + 3 * deltf)  * N / fs)
    main_3X, max_fvalues_main3X,_a =  get_section_freq_max(real_amp, freq_x, main_3X_point_left, main_3X_point_right)
    main_4X_point_left = int((main_4X - 3 * deltf)  * N / fs)
    main_4X_point_right = int((main_4X + 3 * deltf)  * N / fs)
    main_4X, max_fvalues_main4X,_a =  get_section_freq_max(real_amp, freq_x, main_4X_point_left, main_4X_point_right)

    main_5X_point_left = int((main_5X - 3 * deltf)  * N / fs)
    main_5X_point_right = int((main_5X + 3 * deltf)  * N / fs)
    main_5X, max_fvalues_main5X,_a =  get_section_freq_max(real_amp, freq_x, main_5X_point_left, main_5X_point_right)
    main_6X_point_left = int((main_6X - 3 * deltf)  * N / fs)
    main_6X_point_right = int((main_6X + 3 * deltf)  * N / fs)
    main_6X, max_fvalues_main6X,_a =  get_section_freq_max(real_amp, freq_x, main_6X_point_left, main_6X_point_right)

    return max_fvalues_main2X, max_fvalues_main3X, max_fvalues_main4X,max_fvalues_main5X,max_fvalues_main6X


def electricity_feature(x,y,z,fs):
    N = len(x)
    rms_x = np.sqrt(np.sum(np.power(x, 2)/N))
    fengzhi_x = np.max(x)
    CF_x = fengzhi_x / rms_x

    rms_y = np.sqrt(np.sum(np.power(y, 2)/N))
    fengzhi_y = np.max(y)
    CF_y = fengzhi_y / rms_y

    rms_z = np.sqrt(np.sum(np.power(z, 2)/N))
    fengzhi_z = np.max(z)
    CF_z = fengzhi_z / rms_z
    # 三相电流平衡性：
    rms_mean = np.mean([rms_x,rms_y,rms_z])
    print(f'rms_mean={rms_mean}')
    rms_max = np.max([rms_x,rms_y,rms_z])
    print(f'rms_max={rms_max}')
    unbalance_degree = (rms_max-rms_mean)/rms_mean
    # 三相电流频率,A,B,C
    deltf = fs / N
    main_1X = 50
    freq_x1,real_amp1,_a = fft_plot(x, fs)
    freq_x1,real_amp2,_a = fft_plot(y, fs)
    freq_x1,real_amp3,_a = fft_plot(z, fs)

    main_1Xwucha = 3*deltf
    main_freq_left = main_1X - main_1Xwucha
    main_point_left = int(main_freq_left * N / fs)
    main_freq_right = main_1X + main_1Xwucha
    main_point_right = int(main_freq_right * N / fs) 
    max_f1,max_fvalues1,max_value_loc1 = get_section_freq_max(real_amp1,freq_x1, main_point_left, main_point_right)
    max_f2,max_fvalues2,max_value_loc2 = get_section_freq_max(real_amp2,freq_x1, main_point_left, main_point_right)
    max_f3,max_fvalues3,max_value_loc3 = get_section_freq_max(real_amp3,freq_x1, main_point_left, main_point_right)
    print(f'max_f1={max_f1},max_fvalues1={max_fvalues1}')
    # THD, 1~n ,1~6
    max_fvalues_main2X, max_fvalues_main3X, max_fvalues_main4X,max_fvalues_main5X,max_fvalues_main6X = get_n_harmonic(fs,max_f1,real_amp1, freq_x1, N, deltf) #A
    THDAsum = max_fvalues_main2X**2 + max_fvalues_main3X**2 + max_fvalues_main4X**2 + max_fvalues_main5X**2 + max_fvalues_main6X**2 #np.sqrt()
    THD_A = np.sqrt(THDAsum)/max_fvalues1

    max_fvalues_main2X, max_fvalues_main3X, max_fvalues_main4X,max_fvalues_main5X,max_fvalues_main6X = get_n_harmonic(fs,max_f2,real_amp2, freq_x1, N, deltf) #A
    THDBsum = max_fvalues_main2X**2 + max_fvalues_main3X**2 + max_fvalues_main4X**2 + max_fvalues_main5X**2 + max_fvalues_main6X**2 #np.sqrt()
    THD_B = np.sqrt(THDBsum)/max_fvalues2

    max_fvalues_main2X, max_fvalues_main3X, max_fvalues_main4X,max_fvalues_main5X,max_fvalues_main6X = get_n_harmonic(fs,max_f3,real_amp3, freq_x1, N, deltf) #A
    THDCsum = max_fvalues_main2X**2 + max_fvalues_main3X**2 + max_fvalues_main4X**2 + max_fvalues_main5X**2 + max_fvalues_main6X**2 #np.sqrt()
    THD_C = np.sqrt(THDCsum)/max_fvalues3

    
    return rms_x,rms_y,rms_z,fengzhi_x,fengzhi_y,fengzhi_z,CF_x,CF_y,CF_z,unbalance_degree,max_f1,max_f2,max_f3,THD_A,THD_B,THD_C


if __name__ == '__main__':
    # path_ref_wave_xyz = r'G:\12-1-21 - 副本.csv'
    # fs = 2560
    #----德国两相数据------
    # path_ref_wave_x = r'G:\电流故障诊断\phase_current_1.csv'
    # path_ref_wave_y = r'G:\电流故障诊断\phase_current_2.csv'
    # path_ref_wave_z = r'G:\电流故障诊断\phase_current_1.csv'
    # fs = 64000
    #----CSDN--偏心--bias--
    path_ref_wave_x = r'G:\电流故障诊断\bias_A_fs5000.csv'
    path_ref_wave_y = r'G:\电流故障诊断\bias_B_fs5000.csv'
    path_ref_wave_z = r'G:\电流故障诊断\bias_C_fs5000.csv'
    fs = 5000
    norm_x = np.loadtxt(path_ref_wave_x)
    norm_y = np.loadtxt(path_ref_wave_y)
    norm_z = np.loadtxt(path_ref_wave_z)
    
    rms_x,rms_y,rms_z,fengzhi_x,fengzhi_y,fengzhi_z,CF_x,CF_y,CF_z,unbalance_degree,max_f1,max_f2,max_f3,THD_A,THD_B,THD_C = electricity_feature(norm_x,norm_y,norm_z,fs)
    print(f'rms_x={rms_x},fengzhi_x={fengzhi_x},CF_x={CF_x},unbalance_degree={unbalance_degree},max_f1={max_f1},max_f2={max_f2},max_f3={max_f3}')
    print(f'THD_A = {THD_A},THD_B={THD_B},THD_C={THD_C}')