'''
calculate distance possibility;

'''

def calculate_feature_possibility(misalignment1X,misalignment2X,misalignment_level,speed_freq_values):
    # misalignment1X_5 = misalignment1X.copy()
    # misalignment1X_5.append(misalignment1X[3]*2)
    # misalignment2X_5 = misalignment2X.copy()
    # misalignment2X_5.append(misalignment2X[3]*2)
    # 0.6 + 0.4/(1 - model_bearing_BPFI_para[0])*(bearing_BPFI_enery_env_ratio-model_bearing_BPFI_para[0])
    # misalignment_1X_possibility = 0.6 + 0.4/(misalignment1X_5[misalignment_level] - misalignment1X_5[misalignment_level-1])*(speed_freq_values[0]-misalignment1X_5[misalignment_level-1])
    misalignment_1X_possibility = 0.6 + 0.4/(1 - misalignment1X[0])*(speed_freq_values[0]-misalignment1X[0])
    # misalignment_2X_possibility = 0.6 + 0.4/(misalignment2X_5[misalignment_level] - misalignment2X_5[misalignment_level-1])*(speed_freq_values[1]-misalignment2X_5[misalignment_level-1])
    misalignment_2X_possibility = 0.6 + 0.4/(1 - misalignment2X[0])*(speed_freq_values[1]-misalignment2X[0])
    # speed_freq_possibility[1] = (misalignment_1X_possibility + misalignment_2X_possibility)/2
    return misalignment_1X_possibility,misalignment_2X_possibility