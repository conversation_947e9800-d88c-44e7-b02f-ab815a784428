'''
无量纲指标计算:
date:20250211;
时域指标：平均幅值,平均值,峰值,峰峰值,方根幅值,最大值,最小值
无量纲指标:脉冲指标--波形指标--倾斜度--峰值指标--峭度--裕度指标

'''

import numpy as np
import pandas as pd
# from scipy_fft import fft_plot


class VibrationIndicators:
    """振动波形数据的无量纲指标计算"""
    
    def __init__(self, data):
        """
        初始化数据
        data: 振动时域信号数据
        """
        self.data = np.array(data)-np.mean(data)
        # self.mean = np.mean(self.data)
        self.n = len(data)
        self.rms = np.sqrt(np.mean(np.square(self.data)))  # 均方根值
        
    def crest_factor(self):
        """
        峰值因子 = 最大绝对值/RMS值
        反映信号中的冲击性
        """
        peak = np.max(np.abs(self.data))
        return peak / self.rms if self.rms != 0 else 0
    
    def clearance_factor(self):
        """
        裕度因子 = 峰值/方根幅值
        对早期故障更敏感
        """
        mean_abs = np.power(np.sum(np.sqrt(np.abs(self.data)))/self.n, 2)
        peak = np.max(np.abs(self.data))
        return peak / mean_abs if mean_abs != 0 else 0
    
    def impulse_factor(self):
        """
        脉冲因子 = 最大绝对值/算术平均绝对值
        反映冲击程度
        """
        mean_abs = np.mean(np.abs(self.data))
        peak = np.max(np.abs(self.data))
        return peak / mean_abs if mean_abs != 0 else 0
    
    def shape_factor(self):
        """
        波形因子 = RMS值/平均绝对值
        反映波形偏离正弦波的程度
        """
        mean_abs = np.mean(np.abs(self.data))
        return self.rms / mean_abs if mean_abs != 0 else 0
    
    def kurtosis(self):
        """
        峭度 = 四阶中心矩/标准差的四次方
        反映信号的脉冲性
        """
        mean = np.mean(self.data)
        std = np.std(self.data, ddof=1)
        if std == 0:
            return 0
        return np.sum((self.data - mean) ** 4) / (self.n * std ** 4)
    
    def skewness(self):
        """
        偏度 = 三阶中心矩/标准差的三次方
        反映波形的对称性
        """
        mean = np.mean(self.data)
        std = np.std(self.data, ddof=1)
        if std == 0:
            return 0
        return np.sum((self.data - mean) ** 3) / (self.n * std ** 3)

def wilianggang(x):
    x=np.array(x)
    N = x.shape
    average = np.mean(x) #
    x = x - average
    #----计算rms有效值-----
    rms = np.sqrt(np.sum(np.power(x, 2)/N))
    fanggenfuzhi = np.power(np.sum(np.sqrt(np.abs(x)))/N, 2)
    average_amplitude = np.sum(np.abs(x))/N
    #---计算峰值---
    temp = sorted(x, reverse=True)
    fengzhi = np.mean(temp[:10])
    fengzhi_gu = np.mean(temp[-10:])
    #------------不做排序-----------------
    # fengzhi = np.mean(temp[0])
    # fengzhi_gu = np.mean(temp[-1])
    #峰峰值
    ff = fengzhi - fengzhi_gu
    #波形指标----------------------------------------------------------
    K = rms/average_amplitude
    #峰值指标--------------------------------------------------
    C = fengzhi/rms
    #脉冲指标------------------------------------------------------------
    I = fengzhi/average_amplitude
    #峭度----------------------------------------------------------------
    qx = pd.Series(x).skew()
    qd = pd.Series(x).kurt() + 3
    #裕度指标--------------------------------------------------------------
    Ce = fengzhi/fanggenfuzhi
    # result_feature2 = np.array([rms, average_amplitude[0], average, fengzhi, ff, I[0], K[0], qx, C, fanggenfuzhi[0], qd, fengzhi, fengzhi_gu, Ce[0]])
    result_feature = np.array([ I[0], K[0], qx, C,  qd, Ce[0]])
    result_feature = result_feature.tolist()
    result_feature2 = np.array([rms, average_amplitude[0], average, fengzhi, ff, fanggenfuzhi[0], fengzhi, fengzhi_gu])

    return result_feature,result_feature2


if __name__ == '__main__':
    path_ref_wave_xyz = r'G:\12-1-21 - 副本.csv'
    norm_x = np.loadtxt(path_ref_wave_xyz)
    result_featurexx,result_featurexx2 = wilianggang(norm_x)
    print(f'脉冲指标--波形指标--倾斜度--峰值指标--峭度--裕度指标')
    print(result_featurexx)
    print(f'result_feature2')
    print(f'有效值--平均幅值,平均值,峰值,峰峰值,方根幅值,最大值,最小值')
    print(result_featurexx2)
    print("end")

# 使用示例
    vi = VibrationIndicators(norm_x)
    
    print(f"峰值因子: {vi.crest_factor():.4f}")
    print(f"裕度因子: {vi.clearance_factor():.4f}")
    print(f"脉冲因子: {vi.impulse_factor():.4f}")
    print(f"波形因子: {vi.shape_factor():.4f}")
    print(f"峭度: {vi.kurtosis():.4f}")
    print(f"偏度: {vi.skewness():.4f}")