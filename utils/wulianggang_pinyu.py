#---无量纲频域--20250211,
# 合并:
#重心频率,均方根频率,频率方差,频率峭度,频率偏度,谱熵,RMS谱带宽,3-dB谱带宽,95%能量带宽,谱平坦度,
#分段谱平坦度1~8;

import numpy as np
from scipy.fft import fft
from scipy import stats
from scipy_fft_test import fft_plot
from scipy import signal
import matplotlib.pyplot as plt


class FrequencyDomainIndicators:
    """振动波形数据的频域无量纲指标计算"""
    
    def __init__(self, data, fs):
        """
        初始化数据
        data: 时域振动信号
        fs: 采样频率
        """
        self.data = np.array(data)
        self.fs = fs
        self.n = len(data)
        
        # 计算频谱
        self.fft_data = fft(self.data)
        self.amplitude = np.abs(self.fft_data)[:self.n//2]
        self.freq = np.fft.fftfreq(self.n, 1/self.fs)[:self.n//2]
        
        # 功率谱
        self.power_spectrum = np.square(self.amplitude) / self.n
        # other method
        # freqs, self.power_spectrum = signal.welch(self.data,fs=self.fs,nperseg=min(256, self.n),scaling='density')
        # self.freq = freqs
        plt.plot(self.freq,self.power_spectrum)
        plt.title('power_spectrum')
        plt.show()
        
    def frequency_center(self):
        """
        重心频率（FC）-ok
        反映频谱分布的--中心位置
        """
        return np.sum(self.freq * self.power_spectrum) / np.sum(self.power_spectrum)
    
    def mean_square_frequency(self):
        """
        均方频率（MSF）-o
        反映频谱分布的--离散程度
        """
        return np.sum(np.square(self.freq) * self.power_spectrum) / np.sum(self.power_spectrum)
    
    def root_mean_square_frequency(self):
        """
        均方根频率（RMSF）-ok
        """
        return np.sqrt(self.mean_square_frequency())
    
    def frequency_variance(self):
        """
        频率方差（VF）--ok
        反映频谱分布的--集中程度
        """
        fc = self.frequency_center()
        return np.sum(np.square(self.freq - fc) * self.power_spectrum) / np.sum(self.power_spectrum)
    
    def frequency_kurtosis(self):
        """
        频率峭度
        反映频谱分布的陡峭程度？
        """
        fc = self.frequency_center()
        vf = self.frequency_variance()
        if vf == 0:
            return 0
        return np.sum(np.power(self.freq - fc, 4) * self.power_spectrum) / (np.sum(self.power_spectrum) * np.power(vf, 2))
    
    def frequency_skewness(self):
        """
        频率偏度
        反映频谱分布的不对称性？
        """
        fc = self.frequency_center()
        vf = self.frequency_variance()
        if vf == 0:
            return 0
        return np.sum(np.power(self.freq - fc, 3) * self.power_spectrum) / (np.sum(self.power_spectrum) * np.power(vf, 1.5))
    
    def spectral_entropy(self):
        """
        谱熵
        反映频谱的复杂度
        """
        # 归一化功率谱
        norm_power = self.power_spectrum / np.sum(self.power_spectrum)
        # 去除零值避免log计算错误
        norm_power = norm_power[norm_power > 0]
        return -np.sum(norm_power * np.log2(norm_power))
    #
    def spectral_bandwidth_rms(self):
        """
        RMS谱带宽?ok
        基于频率的均方根偏差
        """
        # 计算重心频率
        fc = np.sum(self.freq * self.power_spectrum) / np.sum(self.power_spectrum)
        # 计算RMS带宽
        bandwidth = np.sqrt(np.sum(np.square(self.freq - fc) * self.power_spectrum) / np.sum(self.power_spectrum))
        return bandwidth
    
    def spectral_bandwidth_x_db(self, x=3):
        """
        X-dB谱带宽
        信号在最大幅值下降X-dB时的频率范围
        
        参数:
        x: 下降的分贝数（默认3dB）
        """
        # 将幅度转换为dB
        spectrum_db = 20 * np.log10(self.amplitude / np.max(self.amplitude))
        
        # 找到高于-x dB的频率点
        mask = spectrum_db > -x
        if not np.any(mask):
            return 0
        
        # 计算带宽（最高频率点 - 最低频率点）
        freq_range = np.fft.fftfreq(self.n, 1/self.fs)[:self.n//2]
        valid_freqs = freq_range[mask]
        
        return valid_freqs[-1] - valid_freqs[0]
    
    def spectral_bandwidth_percentage(self, percentage=0.95):
        """
        百分比带宽?ok
        包含信号总能量指定百分比的频率范围
        
        参数:
        percentage: 能量百分比（默认95%）
        """
        # 计算累积能量
        cumulative_energy = np.cumsum(self.power_spectrum)
        print(cumulative_energy.shape)
        total_energy = cumulative_energy[-1]
        
        # 找到-达到指定能量百分比的频率点
        energy_threshold = percentage * total_energy
        idx = np.where(cumulative_energy >= energy_threshold)[0][0]
        
        return self.freq[idx]
    
    def spectral_flatness(self):
        """
        谱平坦度?ok
        几何平均值与算术平均值的比值
        范围：0-1，越接近1表示越平坦（白噪声）
        """
        psd_nonzero = self.power_spectrum[self.power_spectrum > 0]  # 避免log计算错误
        
        if len(psd_nonzero) == 0:
            return 0
            
        geometric_mean = np.exp(np.mean(np.log(psd_nonzero)))
        arithmetic_mean = np.mean(psd_nonzero)
        geometric_mean_test = np.exp(np.mean(np.log(psd_nonzero/arithmetic_mean)))
        # print(f'geometric_mean_test={geometric_mean_test},VS {geometric_mean / arithmetic_mean}')
        return geometric_mean / arithmetic_mean if arithmetic_mean != 0 else 0


    def spectral_flatness_bands(self, num_bands=8):
        """
        分段谱平坦度?ok
        将频谱分成多个频带，分别计算平坦度
        
        参数:
        num_bands: 频带数量
        """
        # 将频谱分成等分的频带
        band_indices = np.array_split(range(len(self.power_spectrum)), num_bands)
        flatness_per_band = []
        
        for indices in band_indices:
            band_psd = self.power_spectrum[indices]
            band_psd_nonzero = band_psd[band_psd > 0]
            
            if len(band_psd_nonzero) > 0:
                geometric_mean = np.exp(np.mean(np.log(band_psd_nonzero)))
                arithmetic_mean = np.mean(band_psd_nonzero)
                flatness = geometric_mean / arithmetic_mean if arithmetic_mean != 0 else 0
            else:
                flatness = 0
                
            flatness_per_band.append(flatness)
            
        return np.array(flatness_per_band)


# 使用示例
if __name__ == '__main__':
    # 生成示例振动数据（包含多个频率成分）
    # t = np.linspace(0, 1, 1000)
    # fs = 1000  # 采样频率
    # # 生成包含50Hz和100Hz的信号
    # signal = np.sin(2 * np.pi * 50 * t) + 0.5 * np.sin(2 * np.pi * 100 * t) + 0.1 * np.random.randn(1000)
    path_ref_wave_xyz = r'G:\12-1-21 - 副本.csv'
    fs = 5120

    signal_test = np.loadtxt(path_ref_wave_xyz)
    # huatu test
    freq_x, real_amp = fft_plot(signal_test,fs)
    plt.plot(freq_x,real_amp)
    plt.show()
    # 计算频域指标
    fdi = FrequencyDomainIndicators(signal_test, fs)
    
    print(f"重心频率: {fdi.frequency_center():.2f} Hz")
    print(f"均方频率: {fdi.mean_square_frequency():.2f} Hz²")
    print(f"均方根频率: {fdi.root_mean_square_frequency():.2f} Hz")
    print(f"频率方差: {fdi.frequency_variance():.2f} Hz²")
    print(f"频率峭度: {fdi.frequency_kurtosis():.2f}")
    print(f"频率偏度: {fdi.frequency_skewness():.2f}")
    print(f"谱熵: {fdi.spectral_entropy():.2f}")

    print(f"RMS谱带宽: {fdi.spectral_bandwidth_rms():.2f} Hz")
    print(f"3-dB谱带宽: {fdi.spectral_bandwidth_x_db(3):.2f} Hz")
    print(f"95%能量带宽: {fdi.spectral_bandwidth_percentage(0.95):.2f} Hz")
    print(f"谱平坦度: {fdi.spectral_flatness():.4f}")
    print("\n分段谱平坦度:")
    for i, flatness in enumerate(fdi.spectral_flatness_bands()):
        print(f"频带 {i+1}: {flatness:.4f}")