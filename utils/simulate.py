'''
date:20250307,-模拟电流故障
'''
import numpy as np
from scipy.signal import hilbert


# 模拟正常和故障情况下的电流信号
def simulate_current_signal(motor_para,fault_type='normal', fault_degree=0.1, fs=1000, t=5):
    """
    根据故障类型生成电流信号。
    :param fault_type: 故障类型，包括'normal', 'loose_stator', 'eccentric_stator', 'core_fault', 'eccentric_rotor', 'broken_bar'
    :param fault_degree: 故障程度，范围从0到1
    :param fs: 采样频率
    :param t: 时间长度
    :return: 时间轴和对应的电流信号
    """
    print(f'motor_para = {motor_para}')
    np.random.seed(0)
    FL = motor_para['FL']
    P = motor_para['P']
    RS = motor_para['RS']
    M =  motor_para['M']
    Z = motor_para['Z']
    A = motor_para['A']
    V = motor_para['V']
    SS = 2*FL/P * 60
    s =(SS-RS)/60
    print(f's={s}')
    Fp = s*P
    print(f'Fp={Fp}')
    # Fp = 2*s*50 # (1+2s)f  (1-2s)
    time = np.linspace(0, t, int(fs*t))
    current = A*np.sin(2*np.pi*FL*time)  # 基础正弦波形代表50Hz电源
    
    if fault_type == 'loose_stator':
        # 定子绕组松动可能会导致额外的谐波成分
        current += fault_degree * np.sin(2*np.pi*2*FL*time) #150
    elif fault_type == 'eccentric_stator':
        # 定子偏心可能会引入相位调制A * np.sin(2 * np.pi * f * t) * (1 + 0.1 * np.sin(2 * np.pi * 0.1 * f * t))
        current += fault_degree*np.sin(2*np.pi*(RS/60)*time)
    elif fault_type == 'core_fault':
        # 铁芯故障可能表现为局部放电或磁通泄露，这里简单地增加一些噪声
        current += fault_degree * np.random.randn(len(time))
    elif fault_type == 'eccentric_rotor':
        # 转子偏心可能导致频率调制
        current += fault_degree*np.sin(2*np.pi*(RS/60)*time)
        current += fault_degree*np.sin(2*np.pi*(M*RS/60)*time)
    elif fault_type == 'broken_bar':
        # 转子断条可能产生边带频率,Fl/Fp
        sideband_left = fault_degree * np.sin(2*np.pi*(FL-Fp)*time)
        sideband_right = fault_degree * np.sin(2*np.pi*(FL+Fp)*time)
        current = current+ sideband_left + sideband_right
        # current += sideband_left
    
    return time, np.real(current)


# 示例：生成并分析一个有转子断条故障的电流信号
if __name__ == "__main__":
    '''
    输入：电机参数:
        电源频率:FL,50
        极数:P,6
        同步转速:SS: 2*FL/P;(Hz), *60 rpm, 
        额定转速,992 rpm, RS
        转差率,(1000-992)/60
        转子条数,24  M
        定子槽数,72  Z
        额定电流,100A
        额定电压,380V
    '''
    motor_para = {'FL':50,'P':6,'RS':992,'M':24,'Z':72,'A':100,'V':380}
    time, current = simulate_current_signal(motor_para,fault_type='broken_bar', fault_degree=0.3) #转子断条
    # time, current = simulate_current_signal(motor_para,fault_type='loose_stator', fault_degree=0.3) #定子绕组松动
    # time, current = simulate_current_signal(motor_para,fault_type='eccentric_stator', fault_degree=0.3) #定子偏心
    # time, current = simulate_current_signal(motor_para,fault_type='core_fault', fault_degree=0.3) #铁芯故障
    # time, current = simulate_current_signal(motor_para,fault_type='eccentric_rotor', fault_degree=0.3) #转子偏心
    
    # detect_fault(current)
    # wave_temp = current
