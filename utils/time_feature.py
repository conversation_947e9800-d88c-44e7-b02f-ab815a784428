# -*- coding: UTF-8 -*-
# 14个特征值:list[14]
#date:20240826,修改最大值-max,最小值=min
# [0]: rms, 有效值
#[1] 平均幅值, 
#[2] 平均值, 
#[3] 峰值, --取了10个较大的平均值
#[4] 峰峰值, --取了10个较大的平均值,10个较小的平均值
#[5] 脉冲指标, 
#[6] 波形指标, 
#[7] 倾斜度, 
#[8] 峰值指标, 
#[9] 方根幅值, 
#[10] 峭度, 
#[11] 最大值, 
#[12] 最小值, 
#[13] 裕度指标
#-------------
import numpy as np
import pandas as pd
# from scipy_fft import fft_plot

def time_feature(x):
    x=np.array(x)
    N = x.shape
    average = np.mean(x) #
    x = x - average
    #----计算rms有效值-----
    rms = np.sqrt(np.sum(np.power(x, 2)/N))
    fanggenfuzhi = np.power(np.sum(np.sqrt(np.abs(x)))/N, 2)
    average_amplitude = np.sum(np.abs(x))/N
    #---计算峰值---
    temp = sorted(x, reverse=True)
    fengzhi = np.mean(temp[:10])
    fengzhi_gu = np.mean(temp[-10:])
    #峰峰值
    ff = fengzhi - fengzhi_gu
    #波形指标
    K = rms/average_amplitude
    #峰值指标
    C = fengzhi/rms
    #脉冲指标
    I = fengzhi/average_amplitude
    #峭度
    qx = pd.Series(x).skew()
    qd = pd.Series(x).kurt() + 3
    #裕度指标
    Ce = fengzhi/fanggenfuzhi
    # result_feature = np.array([rms, average_amplitude[0], average, fengzhi, ff, I[0], K[0], qx, C, fanggenfuzhi[0], qd, fengzhi, fengzhi_gu, Ce[0]])
    result_feature = np.array([rms, average_amplitude[0], average, fengzhi, ff, I[0], K[0], qx, C, fanggenfuzhi[0], qd, temp[0], temp[-1], Ce[0]])
    result_feature = result_feature.tolist()
    return result_feature


if __name__ == '__main__':
    path_ref_wave_xyz = r'G:\12-1-21 - 副本.csv'
    norm_x = np.loadtxt(path_ref_wave_xyz)
    result_featurexx = time_feature(norm_x)
    print(result_featurexx)
    print("end")