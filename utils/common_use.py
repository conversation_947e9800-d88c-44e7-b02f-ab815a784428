'''
common_use function;

'''
import numpy as np


def get_section_freq_max(array_amp,array_freq, point_left, point_right):
    # print(f'array_freq.shape = {array_freq.shape},point_left={point_left},point_right={point_right}')
    if point_left < 0:
        print(f'error set left point,point_left = {point_left}, set to zero!')
        point_left = 0
    if array_amp is None or len(array_amp) == 0:
        return 0,0,0
    # if point_right - point_left >2 and array_amp is not None and len(array_amp) !=0:#bug 6666
    if point_right - point_left >=1 and array_amp is not None and len(array_amp) !=0 and point_left < len(array_amp) and point_right< len(array_amp):#
        max_value_loc = np.argmax(array_amp[point_left:point_right])
    else:
        max_value_loc = 0
        return 0,0,0
    max_value_loc += point_left
    max_fvalues = array_amp[max_value_loc]
    max_f = array_freq[max_value_loc]
    return max_f,max_fvalues,max_value_loc # in bode plot point use max_value_loc


# func(speed_nX,deltf,N,fs,real_amp,freq_x)-->>max_fvalues_speednX
def get_max_fvalues_speednX_value(speed_nX,deltf,N,fs,real_amp,freq_x):
    speed_nX_point_left = int((speed_nX - 3 * deltf)  * N / fs)
    speed_nX_point_right = int((speed_nX + 3 * deltf)  * N / fs)
    speed_nX, max_fvalues_speednX,_a2index =  get_section_freq_max(real_amp, freq_x, speed_nX_point_left, speed_nX_point_right)
    print(f'speed_nX={speed_nX},max_fvalues_speednX = {max_fvalues_speednX}')
    return max_fvalues_speednX


# # 1X ~4X for freq and envelope:
def get_multi_freq_amplitide(fs,main_1X,real_amp, freq_x, N, deltf):
    main_1Xwucha = 3*deltf
    main_freq_left = main_1X - main_1Xwucha
    if main_freq_left< 0:
        print(f'warnging:error,left-point beyond zeros,set 1,main_freq_left = {main_freq_left}')
        main_freq_left = 1
    main_point_left = int(main_freq_left * N / fs)
    main_freq_right = main_1X + main_1Xwucha
    main_point_right = int(main_freq_right * N / fs)  
    main_1X_real, max_fvalues_main1X,_a =  get_section_freq_max(real_amp,freq_x, main_point_left, main_point_right)
    main_2X = main_1X_real * 2
    main_3X = main_1X_real * 3
    main_4X = main_1X_real * 4
    if main_2X > fs/2.56 or main_3X > fs/2.56 or main_4X > fs/2.56:
        print('!!!!---error!!!--fs set ! detect main_1~4X beyond the anysistic frequency!!!!')
        return 0,0,0,0,0
    main_2X_point_left = int((main_2X - 3 * deltf)  * N / fs)
    if main_2X_point_left < 0:
        print(f'warnging:error,left-point beyond zeros,set ,main_2X_point_left = {main_2X_point_left}')
        return 0,0,0,0,0
    main_2X_point_right = int((main_2X + 3 * deltf)  * N / fs)
    main_2X, max_fvalues_main2X,_a =  get_section_freq_max(real_amp, freq_x, main_2X_point_left, main_2X_point_right)
    main_3X_point_left = int((main_3X - 3 * deltf)  * N / fs)
    if main_3X_point_left < 0:
        print(f'warnging:error,left-point beyond zeros,set ,main_3X_point_left = {main_3X_point_left}')
        return 0,0,0,0,0
    main_3X_point_right = int((main_3X + 3 * deltf)  * N / fs)
    main_3X, max_fvalues_main3X,_a =  get_section_freq_max(real_amp, freq_x, main_3X_point_left, main_3X_point_right)
    main_4X_point_left = int((main_4X - 3 * deltf)  * N / fs)
    if main_4X_point_left < 0:
        print(f'warnging:error,left-point beyond zeros,set ,main_4X_point_left = {main_4X_point_left}')
        return 0,0,0,0,0
    main_4X_point_right = int((main_4X + 3 * deltf)  * N / fs)
    main_4X, max_fvalues_main4X,_a =  get_section_freq_max(real_amp, freq_x, main_4X_point_left, main_4X_point_right) 
    return main_1X_real, max_fvalues_main1X, max_fvalues_main2X, max_fvalues_main3X, max_fvalues_main4X