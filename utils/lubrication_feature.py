'''
date:20240326;
lubrication_feature:
input:
    data:numpy
    fs: sample freq
output:
    
    1: 润滑底噪故障计算相对值;
    2: 润滑底噪面积计算值;


'''
import pandas as pd
import numpy as np
from numpy import trapz
import os
import sys
CURRENT_PATH = os.path.dirname(os.path.realpath(__file__))
sys.path.append(os.path.join(CURRENT_PATH, '..'))
from utils.scipy_fft import fft_plot
import json

#
def load_dict_from_json(file_path):
    with open(file_path, 'r', encoding='utf-8') as file:
        dictionary = json.load(file)
    return dictionary


def envelope_extraction(signal):
    s = signal.astype(float )
    q_u = np.zeros(s.shape)
    q_l =  np.zeros(s.shape)
    u_x = [0,]
    u_y = [s[0],]
    l_x = [0,]
    l_y = [s[0],]
    for k in range(1,len(s)-1):
        if (np.sign(s[k]-s[k-1])==1) and (np.sign(s[k]-s[k+1])==1):
            u_x.append(k)
            u_y.append(s[k])

        if (np.sign(s[k]-s[k-1])==-1) and ((np.sign(s[k]-s[k+1]))==-1):
            l_x.append(k)
            l_y.append(s[k])
    u_x.append(len(s)-1)
    u_y.append(s[-1])
    l_x.append(len(s)-1)
    l_y.append(s[-1])
    upper_envelope_y = np.zeros(len(signal))
    lower_envelope_y = np.zeros(len(signal))
    upper_envelope_y[0] = u_y[0]
    upper_envelope_y[-1] = u_y[-1]
    lower_envelope_y[0] =  l_y[0]
    lower_envelope_y[-1] =  l_y[-1]
    last_idx,next_idx = 0, 0
    k, b = general_equation(u_x[0], u_y[0], u_x[1], u_y[1])
    for e in range(1, len(upper_envelope_y)-1):
        if e not in u_x:
            v = k * e + b
            upper_envelope_y[e] = v
        else:
            idx = u_x.index(e)
            upper_envelope_y[e] = u_y[idx]
            last_idx = u_x.index(e)
            next_idx = u_x.index(e) + 1
            k, b = general_equation(u_x[last_idx], u_y[last_idx], u_x[next_idx], u_y[next_idx])        
    last_idx,next_idx = 0, 0
    k, b = general_equation(l_x[0], l_y[0], l_x[1], l_y[1])
    for e in range(1, len(lower_envelope_y)-1):
        if e not in l_x:
            v = k * e + b
            lower_envelope_y[e] = v
        else:
            idx = l_x.index(e)
            lower_envelope_y[e] = l_y[idx]
            last_idx = l_x.index(e)
            next_idx = l_x.index(e) + 1
            k, b = general_equation(l_x[last_idx], l_y[last_idx], l_x[next_idx], l_y[next_idx])     
    return upper_envelope_y, lower_envelope_y


def general_equation(first_x,first_y,second_x,second_y):
    A = second_y-first_y
    B = first_x-second_x
    C = second_x * first_y - first_x * second_y
    k = -1 * A / B
    b = -1 * C / B
    return k, b


def feature_extract_freq_env(x,fs):
    x = np.array(x)
    freq_x, real_amp,real_angle = fft_plot(x, fs)
    N = len(x)
    if fs > 10000:
        cut_index = int(2000*N/fs)
    else:
        cut_index = int(len(freq_x)/2)
    freq_x_judge_area_start = int(1000*N/fs)  # hz
    freq_x_judge_area_end = int(N/2.56) #Hz
    upper_envelope_y, lower_envelope_y = envelope_extraction(real_amp)
    area_S_up = trapz(upper_envelope_y[cut_index:], freq_x[cut_index:], dx=0.001)
    area_S_down = trapz(lower_envelope_y[cut_index:], freq_x[cut_index:], dx=0.001)
    rms = np.sqrt(np.sum(np.power(x, 2)/N))
    print(f'rms = {rms},area_S_down={area_S_down},nengliang={np.sum(np.power(x,2))/fs}')
    area_up_down = (area_S_up - area_S_down)
    return area_S_down/rms, area_up_down
    # return area_S_down, area_S_down/rms


if __name__ == '__main__':
    import ast
    #--读取新安: 315振动筛激振器从动垂直 波形数据
    # data_path_name = r'F:\data\新安润滑振动数据\315振动筛激振器从动垂直\INIT202308250281_fs_5.120_N_8192_rms_40.9974044690_kur_3.3933400471_2023-12-01-07-21-39.csv'  #
    data_path_name = r'D:\data_vibration\芬雷测试数据20241028\test-润滑不良\211传动轴2V-润滑不良正常rms5.429.txt'

    data_path_name = r'D:\data_vibration\芬雷测试数据20241028\test-润滑不良\326传动轴2V润滑不良轻微rms8.904.txt'
    data_path_name = r'D:\data_vibration\芬雷测试数据20241028\test-润滑不良\326-传动轴2V-0-2000Hz有效值2.872.txt'
    # data_path_name = r'D:\data_vibration\芬雷测试数据20241028\test-润滑不良\206-传动轴2V-润滑不良rms16.46.txt'
    # data_path_name = r'D:\data_vibration\芬雷测试数据20241028\test-润滑不良\206-润滑不良发生前rms2.149.txt'
    # data_path_name = r'D:\data_vibration\芬雷测试数据20241028\test-润滑不良\206-0-2000Hz润滑不良rms1.142.txt'

    #测试停机--20241029-
    data_path_name = r'D:\data_vibration\芬雷测试数据20241028\test-润滑不良\206-传动轴0-2000-停机0.027.txt'
    data_path_name = r'D:\data_vibration\芬雷测试数据20241028\test-润滑不良\206-0-20k停机.txt' #
    data_path_name = r'C:\Users\<USER>\Downloads\txt.txt'
    #----20241031----测试一个目录数据读取----------
    path_test = r'D:\data_vibration\芬雷测试数据20241030\陕西-FGRD\8109\传动轴2V\波形数据_20241031084121' # 润滑不良
    path_test = r'\data_vibration\芬雷测试数据20241030\陕西-FGRD\2107正常\传动轴2V\波形数据_20241030135350' # 正常
    #
    #D
    path_test = r'D:\data_vibration\芬雷测试数据20241030\内蒙KD\362\波形数据_20241101091203'
    test_txts = os.listdir(path_test)
    print(f'test_txts={test_txts}')
    for test_csvi in test_txts:
    # data = pd.read_csv(data_path_name,header=None).values
        print(f'test_csvi = {test_csvi}')
        test_csv = os.path.join(path_test,test_csvi)
        # data = np.loadtxt(test_csv) #load_dict_from_json
        my_dict = load_dict_from_json(test_csv)
        # data = eval(my_dict['waveData'])
        data = ast.literal_eval(my_dict['waveData'])
        print(len(data))
        fs = 51200
        # fs = 5120
        area_S_down,area_up_down = feature_extract_freq_env(data,fs)
        print(f'area_S_down={area_S_down},area_up_down={area_up_down}')
    # print(f'area_S_up_per = {area_S_up/rms},area_S_down_per={area_S_down/rms}')
