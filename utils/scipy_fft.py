# -*- coding: utf-8 -*-
# date:20231221,返回的频谱：需提前除去均值,
# date:20240109,add 倒谱、积分速度、位移
#date:20240110, add 频谱图增加相位,
import logging

import numpy as np

from scipy.fftpack import fft,ifft,hilbert


def envelope_plot(x, fs):
    logging.info(f'len(x)={len(x)},type:{type(x)},fs={fs}')
    x = np.array(x)-np.mean(x)
    complex_sig = x.astype(dtype=complex)
    analytic_signal = hilbert(x)  # only imag section
    complex_sig.imag = analytic_signal
    amplitude_envelope = np.abs(complex_sig)
    # here to drop mean
    mean_amp_env = np.mean(amplitude_envelope)
    amplitude_envelope = amplitude_envelope - mean_amp_env
    freq_x, real_amp,real_angle = fft_plot(amplitude_envelope, fs)
    return freq_x, real_amp

#返回
def fft_plot(x, fs):
    logging.info(f'len(x)={len(x)},type:{type(x)},fs={fs}')
    x = np.array(x)-np.mean(x)
    # X = np.fft.fft(x)
    X = fft(x)
    N = len(x)
    mX = np.abs(X)
    real_angle = np.angle(X)
    real_index = int(X.shape[0] / 2.56)
    mX2 = 2 * mX / X.shape[0]  # 2倍序列长度
    real_amp = mX2[:real_index]
    real_angle = real_angle[:real_index]
    freq_x = np.zeros(real_index, dtype=float)  # 初始化
    for i in range(real_index):  # 生成频率序列
        freq_x[i] = i / N * fs
    return freq_x, real_amp,real_angle

# 倒谱
def receps(x,fs):
    x = np.array(x)-np.mean(x)
    siglen = x.shape[0]
    time_x = np.arange(siglen)/fs
    spectrum = fft(x) #fft(x)
    a = np.log(np.abs(spectrum))
    where_are_inf = np.isinf(a)
    a[where_are_inf] = 0
    ceps = ifft(a).real
    # ceps[0]=0 
    ceps = np.nan_to_num(ceps)
    return time_x,ceps


#速度-时域,c=1000,it=1,fmin=10;fmax=fs/2.56或根据需要
#位移-时域,c=1000,it=2,fmin=10;fmax=fs/2.56或根据需要
def jifen(x, fs, fmin, fmax,c, it):
    x = np.array(x)-np.mean(x)
    N = len(x)
    t = np.arange(N)/fs
    nfft = N #
    y =  fft(x)
    df = fs / nfft
    ni = int(fmin/df+0.5) + 1
    na = int(fmax/df+0.5) + 1
    dw = 2* np.pi * df
    w1 = np.arange(0, 2*np.pi*(0.5*fs), dw)
    w2 = np.arange(-2*np.pi*(0.5*fs),0, dw)
    w=  np.append(w1,w2)
    w = np.power(w, it)
    a = np.zeros((nfft,), dtype=complex)
    a[1:-1] = y[1:-1]/w[1:-1]
    #  进行相位的换算:
    if it == 2:
        y = - a
    else:
        y = a.imag - (0+1j)*a.real
    a = np.zeros((nfft,), dtype=complex)
    a[ni-1:na-1] = y[ni-1:na-1]
    a[nfft-na:nfft-ni] = y[nfft-na:nfft-ni]
    y_re = ifft(a, nfft)
    y_wave = y_re.real * c
    return y_wave


if __name__ == '__main__':
    path_ref_wave_xyz = r'G:\12-1-21 - 副本.csv'
    fs = 2560
    speed = 600 #rpm
    ndeltf = 5 # 几个分辨率
    norm_x =  np.loadtxt(path_ref_wave_xyz)
    norm_x = norm_x - np.mean(norm_x)
    y_wave = jifen(norm_x, fs, 10, 1000,1000, 1)
    freq_x, real_amp,real_angle = fft_plot(norm_x, fs)
    print(f'{real_amp.shape}')



