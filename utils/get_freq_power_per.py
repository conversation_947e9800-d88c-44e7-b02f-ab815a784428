
'''
date:20240522;
计算波形的低频-中频-高频特征:
input: 波形
output: 低频比例,中频,高频;
'''

import numpy as np
import os
import sys
CURRENT_PATH = os.path.dirname(os.path.realpath(__file__))
sys.path.append(os.path.join(CURRENT_PATH, '..'))
from utils.scipy_fft import fft_plot


def get_freq_power_per(norm_x,fs,low_freq,middle_freq,max_freq):

    freq_x, real_amp,_a = fft_plot(norm_x,fs)

    N = len(norm_x)
    low_freq_point = int(low_freq * N/ fs)
    middle_freq_point = int(middle_freq * N/fs)
    max_freq_point = int(max_freq * N /fs)
    #------------------
    section_low_power = np.sum(np.power(real_amp[1:low_freq_point],2))
    section_middle_power = np.sum(np.power(real_amp[low_freq_point:middle_freq_point],2))
    section_high_power = np.sum(np.power(real_amp[middle_freq_point:max_freq_point],2))
    # print(f'section_low_power={section_low_power},section_middle_power={section_middle_power},section_high_power={section_high_power}')
    total_power = section_low_power + section_middle_power + section_high_power
    # print(f'total_power = {total_power}')
    # print(f'low:{section_low_power/total_power},middle:{section_middle_power/total_power},high:{section_high_power/total_power}')
    return section_low_power/total_power,section_middle_power/total_power,section_high_power/total_power

if __name__ == '__main__':
    fs = 5120
    low_freq = 200
    middle_freq = 1000
    max_freq = fs/2.56
    path_csv = r'F:\data\新安点检验证数据2\INIT202308250263\INIT202308250263_fs_5.120_N_8192_rms_38.3095639549_kur_3.6428910385_2024-04-23-10-16-55.csv'
    norm_x =  np.loadtxt(path_csv)
    norm_x = norm_x - np.mean(norm_x)
    a1,a2,a3 = get_freq_power_per(norm_x,fs,low_freq,middle_freq,max_freq)
    print(f'a1={a1},a2={a2},a3={a3}')