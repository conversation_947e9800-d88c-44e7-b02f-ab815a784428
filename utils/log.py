from db.sqlite_db import SQLiteDB

from sys_log import logger

def insert_diagnosis_log(industry, source, device_name, device_type, device_num,
                      device_power, device_speed, position_name, position_type, 
                      bearing_num, gear_count, component_speed, sampling, 
                      frequency, origin_time, model_name):
    db = SQLiteDB()
    columns = [
        'industry', 'source', 'device_name', 'device_type', 'device_num',
        'device_power', 'device_speed', 'position_name', 'position_type',
        'bearing_num', 'gear_count', 'component_speed', 'sampling', 
        'frequency', 'origin_time', 'model_name'
    ]
    values = [
        industry, source, device_name, device_type, device_num,
        device_power, device_speed, position_name, position_type,
        bearing_num, gear_count, component_speed, sampling,
        frequency, origin_time, model_name
    ]
    return db.execute_insert('mm_diagnosis_log', columns, values)
