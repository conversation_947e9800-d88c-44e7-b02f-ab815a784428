from config.config_rabbitmq import load_config
from config.config_rabbitmq_push import load_config_push
from config.config_env import env_name  # 导入环境名称变量
from rabbitmq.rabbitmq import RabbitMQSubscriber

mq_host, mq_port, mq_username, mq_password, mq_virtual_host, mq_exchange, mq_queue_name, mq_routing_key = load_config(env_name)
mq_push_host, mq_push_port, mq_push_username, mq_push_password, mq_push_virtual_host, mq_push_exchange, mq_push_queue_name, mq_push_routing_key = load_config_push(env_name)


def main():
    print("初始化参数：")
    # 打印参数值
    print("MQ Host:", mq_host)
    print("MQ Port:", mq_port)
    print("MQ Username:", mq_username)
    print("MQ Password:", mq_password)
    print("MQ Virtual Host:", mq_virtual_host)
    print("MQ Exchange:", mq_exchange)
    print("MQ Queue Name:", mq_queue_name)
    print("MQ Routing Key:", mq_routing_key)
    subscriber = RabbitMQSubscriber(mq_host, mq_port, mq_username, mq_password, mq_virtual_host, mq_exchange, mq_queue_name, mq_routing_key, mq_push_host, mq_push_port, mq_push_username, mq_push_password, mq_push_virtual_host, mq_push_exchange, mq_push_queue_name, mq_push_routing_key)

    # 创建一个新的线程来启动消费者
    subscriber.start()

if __name__ == "__main__":
    main()