# 使用基础镜像
FROM crpi-5mfi8ivth0edtwf9.cn-hangzhou.personal.cr.aliyuncs.com/zhy-space/python:3.10

# 设置工作目录
WORKDIR /app

# 创建 mnt 目录
RUN mkdir -p /mnt

# 复制应用程序代码到容器中
COPY . /app

# 添加 mm_database.db 到 /mnt（假设它在构建上下文根目录）
COPY mm_database.db /mnt/mm_database.db

# 安装依赖项
RUN pip3 config set global.index-url https://pypi.tuna.tsinghua.edu.cn/simple

RUN pip3 install --upgrade pip
RUN pip install --no-cache-dir -r requirements.txt


# 设置环境变量
ENV PYTHONUNBUFFERED=1

# 运行应用程序
CMD ["python", "main_rabbitmq.py"]

