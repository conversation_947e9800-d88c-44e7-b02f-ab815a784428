from config.config_mqtt import load_mqtt_config
from config.config_mqtt_push import load_mqtt_push_config
from config.config_env import env_name  # 导入环境名称变量
from mqtt.mqtt import MQTTSubscriber

# 加载配置
mqtt_host, mqtt_port, mqtt_username, mqtt_password, mqtt_client_id, mqtt_keep_alive, mqtt_topic = load_mqtt_config(env_name)
mqtt_push_host, mqtt_push_port, mqtt_push_username, mqtt_push_password, mqtt_push_client_id, mqtt_push_keep_alive, mqtt_push_topic = load_mqtt_push_config(env_name)


def main():
    print("初始化参数：")
    print("MQTT Host:", mqtt_host)
    print("MQTT Port:", mqtt_port)
    print("MQTT Username:", mqtt_username)
    print("MQTT Topic:", mqtt_topic)

    subscriber = MQTTSubscriber(mqtt_host, mqtt_port, mqtt_username, mqtt_password, mqtt_client_id, mqtt_topic, mqtt_push_host, mqtt_push_port, mqtt_push_username, mqtt_push_password, mqtt_push_client_id, mqtt_push_topic)
    subscriber.start()


if __name__ == "__main__":
    main()