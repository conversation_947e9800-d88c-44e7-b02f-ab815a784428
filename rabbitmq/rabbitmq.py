import threading
import time
import pika
import json
import numpy as np
import re

from utils.log import insert_diagnosis_log
from sys_log import logger
from main_diagnosis import run

class ArgsObject:
    pass

class RabbitMQSubscriber(threading.Thread):
    def __init__(self, host, port, username, password, virtual_host, exchange, queue_name, routing_key, push_host, push_port, push_username, push_password, push_virtual_host, push_exchange, push_queue_name, push_routing_key):
        super().__init__()
        self.host = host
        self.port = port
        self.username = username
        self.password = password
        self.virtual_host = virtual_host
        self.exchange = exchange
        self.queue_name = queue_name
        self.routing_key = routing_key
        
        self.push_host = push_host
        self.push_port = push_port
        self.push_username = push_username
        self.push_password = push_password
        self.push_virtual_host = push_virtual_host
        self.push_exchange = push_exchange
        self.push_queue_name = push_queue_name
        self.push_routing_key = push_routing_key

        self.should_stop = threading.Event()

    def connect(self):
        credentials = pika.PlainCredentials(self.username, self.password)
        parameters = pika.ConnectionParameters(self.host, self.port, virtual_host=self.virtual_host, credentials=credentials, heartbeat=600, blocked_connection_timeout=300)

        self.connection = pika.BlockingConnection(parameters)
        self.channel = self.connection.channel()

    def setup_queue(self):
        # 声明交换机
        self.channel.exchange_declare(exchange=self.exchange, durable=True, exchange_type='fanout')
        # 声明队列
        self.channel.queue_declare(queue=self.queue_name, durable=True)
        # 将队列绑定到交换机，并指定routing_key
        self.channel.queue_bind(exchange=self.exchange, queue=self.queue_name, routing_key=self.routing_key)

    def disconnect(self):
        if self.connection and self.connection.is_open:
            self.connection.close()

    def run(self):

        while not self.should_stop.is_set():
            try:
                self.connect()
                self.setup_queue()
                self.channel.basic_consume(queue=self.queue_name, on_message_callback=self._on_message, auto_ack=True)
                # 表示每次只预取一条消息给消费者，消费者处理完这条消息后才会预取下一条消息。
                self.channel.basic_qos(prefetch_count=1)

                logger.info("机理模型设备诊断服务v1.0启动成功...")
                self.channel.start_consuming()
            except pika.exceptions.StreamLostError as e:
                print("Connection lost, reconnecting...")
                self.disconnect()
                time.sleep(15)  # 等待一段时间后重新连接
            except Exception as e:
                print("Exception occurred:", e)
                time.sleep(15)  # 等待一段时间后继续尝试连接

    def stop_consuming(self):
        # 停止监听队列
        self.should_stop.set()    

    """
    设备推送信息接收处理：
    {
        "component_id": 1934534483410235393, // 部件ID
        "device_type_name": "循环泵", // 设备类型名称
        "component_name": "驱动端", // 部件名称
        "device_speed": 1500.0, // 设备转速
        "measurement_definition_name": "8k 水平加速度(0.5-6400)", // 测量定义名称
        "industry": "煤炭", // 行业类型
        "device_type": 5, // 设备类型
        "source": "主数据融合测试", // 来源
        "wave_type": 1, // 波形类型
        "fan_number": [16], // 风机叶片数
        "model_ids": [1879060348676943874, 1879060291592466433, 1879060225767059457, 1879060122285191170, 1879059972561121282, 1879059911349448706, 1879059869590958082], // 模型ID列表
        "gearing_para": {"齿轮1": [3000.0, 500.0], "齿轮2": [1500.0, 150.0]}, // 齿轮参数
        "component_type": 3, // 部件类型
        "device_name": "1#循环水泵", // 设备名称
        "device_power": 66.0, // 设备功率
        "wave_data": "/opt/upload/wave/2025/6/16/1934535852083568642/1750032000000", // 波形数据路径
        "bearing_para": [9.258, 6.742, 3.065, 0.421, 0.833, 0.167, 0.417, 0.167], // 轴承参数
        "device_number": "E-001", // 设备编号
        "wave_id": 1934535852083568642, // 波形ID
        "collect_time": "2025-06-16 08:00:00", // 采集时间
        "sampling": 5120, // 采样点数
        "frequency": 8192, // 采样频率
        "component_type_name": "水泵", // 部件类型名称
        "component_speed": 1500.0 // 部件转速
    }
    """
    def _on_message(self, channel, method, properties, body):
        message = body.decode('utf-8')
        logger.info("Received message: %s", message)

        # 将字符串转换为字典
        try:
            dict_result = json.loads(message)
        except json.JSONDecodeError as e:
            logger.error("JSON decode error: %s", e)

        # 将字符串转换为字典
        # 提取新的JSON结构参数
        component_id = dict_result.get("component_id")
        logger.info("Received component_id: %s", component_id)

        device_type_name = dict_result.get("device_type_name")
        logger.info("Received device_type_name: %s", device_type_name)

        component_name = dict_result.get("component_name")
        logger.info("Received component_name: %s", component_name)

        device_speed = dict_result.get("device_speed")
        logger.info("Received device_speed: %s", device_speed)

        measurement_definition_name = dict_result.get("measurement_definition_name")
        logger.info("Received measurement_definition_name: %s", measurement_definition_name)

        industry = dict_result.get("industry")
        logger.info("Received industry: %s", industry)

        device_type = dict_result.get("device_type")
        logger.info("Received device_type: %s", device_type)

        source = dict_result.get("source")
        logger.info("Received source: %s", source)

        wave_type = dict_result.get("wave_type")
        logger.info("Received wave_type: %s", wave_type)

        fan_number = dict_result.get("fan_number")
        logger.info("Received fan_number: %s", fan_number)

        model_ids = dict_result.get("model_ids")
        logger.info("Received model_ids: %s", model_ids)

        gearing_para = dict_result.get("gearing_para")
        logger.info("Received gearing_para: %s", gearing_para)

        component_type = dict_result.get("component_type")
        logger.info("Received component_type: %s", component_type)

        device_name = dict_result.get("device_name")
        logger.info("Received device_name: %s", device_name)

        device_power = dict_result.get("device_power")
        logger.info("Received device_power: %s", device_power)

        wave_data = dict_result.get("wave_data")
        logger.info("Received wave_data: %s", wave_data)

        bearing_para = dict_result.get("bearing_para")
        logger.info("Received bearing_para: %s", bearing_para)

        device_number = dict_result.get("device_number")
        logger.info("Received device_number: %s", device_number)

        wave_id = dict_result.get("wave_id")
        logger.info("Received wave_id: %s", wave_id)

        collect_time = dict_result.get("collect_time")
        logger.info("Received collect_time: %s", collect_time)

        sampling = dict_result.get("sampling")
        logger.info("Received sampling: %s", sampling)

        frequency = dict_result.get("frequency")
        logger.info("Received frequency: %s", frequency)

        component_type_name = dict_result.get("component_type_name")
        logger.info("Received component_type_name: %s", component_type_name)

        component_speed = dict_result.get("component_speed")
        logger.info("Received component_speed: %s", component_speed)

        # 初始化 wave_array 为一个空列表或其他合适的默认值
        wave_array = []
        
        try:
            # 打开文件
            with open(wave_data, 'r') as file:
                # 读取文件内容
                data = file.read()
            # 使用正则表达式找到所有浮点数
            float_regex = r"[-+]?\d*\.?\d+(?:[eE][-+]?\d+)?"        # 将列表转换为 NumPy 数组
            message_value = re.findall(float_regex, data)
            # 将科学计数法表示的字符串转换为浮点数，并去掉科学计数法
            float_data = [float('{:.20f}'.format(float(x))) for x in message_value]

            # 有效值判断
            # if float_data is not None and get_square(float_data) > 0.5:
            # 将一维数据重塑为二维
            wave_array = np.array(float_data)
            # wave_array = float_array.reshape(1, -1)
        except Exception as error:
            print("数据文件读取异常====== %s", error)
            wave_array = []  # 默认空数组

        # 确保 fan_number、gearing_para、bearing_para 不为 None
        fan_number = fan_number if fan_number is not None else []
        gearing_para = gearing_para if gearing_para is not None else {}
        bearing_para = bearing_para if bearing_para is not None else []
        device_number = device_number if device_number is not None else "通用"
        component_speed = component_speed if component_speed is not None else device_speed
        model_ids = model_ids if model_ids is not None else []

        try:
            insert_diagnosis_log(
                industry=industry,
                source=source,
                device_name=device_name,
                device_type=device_type_name,
                device_num=device_number,
                device_power=device_power,
                device_speed=device_speed,
                position_name=component_name,
                position_type=component_type_name,
                bearing_num=str(bearing_para) if bearing_para else None,
                gear_count=len(gearing_para) if gearing_para else 0,
                component_speed=component_speed,
                sampling=sampling,  # 新JSON结构中没有此字段
                frequency=frequency,  # 新JSON结构中没有此字段
                origin_time=collect_time,
                model_name=measurement_definition_name
            )

            args = ArgsObject()
            args.component_id = component_id
            args.device_type_name = device_type_name
            args.component_name = component_name
            args.speed = device_speed
            args.fs = frequency  #
            args.measurement_definition_name = measurement_definition_name
            args.industry = industry
            args.device_type = device_type
            args.source = source
            args.wave_type = wave_type
            args.fan_number = fan_number
            args.model_list = model_ids  # 使用新的model_ids替代model_list ######
            args.gearing_para = gearing_para
            args.component_type = component_type
            args.device_name = device_name
            args.device_power = device_power
            args.wave_data_path = wave_data  # 波形数据路径
            args.bearing_parameter = bearing_para  # 使用新的bearing_para替代bearing_parameter
            args.device_number = device_number
            args.wave_id = wave_id
            args.collect_time = collect_time
            args.component_type_name = component_type_name
            args.component_speed = component_speed
            args.wave_data = wave_array  # 保持原有的波形数据数组
            args.wave_array = wave_array
            # 设置默认的fs和N值，如果新JSON结构中没有提供
            args.fs = getattr(args, 'fs', 5120)  # 默认采样频率
            args.N = getattr(args, 'N', len(wave_array))  # 默认采样点数为波形数据长度
            args.speed = component_speed if component_speed else device_speed  # 设置speed参数
            
            diagnosis_result = run(args)
            logger.info("diagnosis_result====== %s", diagnosis_result)

            # 构建最终的结果字典
            result_dict = {
                "component_id": component_id,
                "wave_id": wave_id,
                "collect_time": collect_time,
                "fault_type_list": diagnosis_result["fault_type_list"],
                "fault_grade_list": diagnosis_result["fault_grade_list"],
                "fault_ratio": diagnosis_result["fault_ratio"],
                "fault_features": diagnosis_result["fault_features"],
                "fault_possibility": diagnosis_result["fault_possibility"]
            }

            logger.info("final result_dict====== %s", result_dict)

            # 消息推送
            push_thread = RabbitMQPusher(self.push_host, self.push_port, self.push_username, self.push_password, self.push_virtual_host, self.push_exchange, self.push_queue_name, self.push_routing_key, json.dumps(result_dict, ensure_ascii=False))
            push_thread.start()
        except Exception as error:
            logger.info("消息推送异常====== %s", error)
       

class RabbitMQPusher(threading.Thread):
    def __init__(self, host, port, username, password, virtual_host, exchange, queue_name, routing_key, message):
        threading.Thread.__init__(self)
        self.host = host
        self.port = port
        self.username = username
        self.password = password
        self.exchange = exchange
        self.virtual_host = virtual_host
        self.queue_name = queue_name
        self.routing_key = routing_key
        self.message = message

    def run(self):
        credentials = pika.PlainCredentials(self.username, self.password)
        parameters = pika.ConnectionParameters(self.host, self.port, virtual_host=self.virtual_host, credentials=credentials, heartbeat=600, blocked_connection_timeout=300)

        connection = pika.BlockingConnection(parameters)
        channel = connection.channel()

        try:
            channel.basic_publish(
                exchange=self.exchange,
                routing_key=self.routing_key,
                body=self.message
            )
            logger.info("Message published: %s", self.message)
        except Exception as error:
            logger.info("消息推送异常====== %s", error)
        finally:
            connection.close()
