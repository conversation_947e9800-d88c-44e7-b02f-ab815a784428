import logging
import os
import time
import configparser
from logging.handlers import RotatingFileHandler
from config.config_env import env_path  # 导入路径变量

script_dir = os.path.dirname(os.path.abspath(__file__))  # 获取脚本所在的目录
config = configparser.ConfigParser()

# 拼接配置文件路径
log_dir = f'{env_path}logs'
# 读取外部配置文件
if os.path.exists(log_dir):
    config.read(log_dir)
else:
    raise FileNotFoundError(f"Configuration file '{log_dir}' not found.")

if not os.path.exists(log_dir):
    os.mkdir(log_dir)

path = log_dir + '/%s' % time.strftime('%Y-%m-%d', time.localtime())      # 日志文件名

logger = logging.getLogger(__name__)
logger.setLevel(logging.DEBUG)

formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')

# 最大文件100M，保留20个
file_handler = RotatingFileHandler(path, maxBytes=1024*1024*100, backupCount=20)
file_handler.setLevel(logging.DEBUG)
file_handler.setFormatter(formatter)

logger.addHandler(file_handler)

logger.info('================= 机理模型设备诊断服务日志 =================')
