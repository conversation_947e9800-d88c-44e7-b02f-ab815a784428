# 1. 机理模型-设备诊断

## 1.1 结构

```
├── MM-DIAGNOSIS                           # 机理模型设备诊断服务
│   ├── config                             # 配置环境
│   ├── db                                 # 数据库驱动
│   ├── doc                                # 说明文档
│   ├── mqtt                               # mqtt消息队列
│   ├── rabbitmq                           # rabbitmq消息队列
│   ├── utils                              # 工具&组装模块
│   ├── .gitignore                		   # ignore
│   ├── requirements.txt                   # 依赖
│   ├── dockerfile                	       # 镜像文件
│   ├── init_sqlite.py                	   # sqlite初始化脚本
│   ├── main_mqtt.py                       # mqtt启动入口
│   ├── main_rabbitmq.py                   # rabbitmq启动入口
│   ├── sys_log.py                	       # 系统日志
│   └── README.md          				   # readme
```

## 1.2 概述

机理模型-设备诊断服务，负责诊断数据接入、模型调用、诊断结果反馈等功能。

## 1.3 模型-设备诊断流程

![mm_diagnosis](./doc/img/mm_diagnosis.jpg)

