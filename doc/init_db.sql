-- 创建诊断日志表
create table mm_diagnosis_log
(
    id INTEGER PRIMARY KEY AUTOINCREMENT, -- 主键，自增ID
    industry VARCHAR(64), -- 行业类型
    source VARCHAR(64), -- 来源
    device_name VARCHAR(64), -- 设备名称
    device_type VARCHAR(64), -- 设备类型
    device_num VARCHAR(64), -- 设备型号
    device_power INTEGER DEFAULT 0 NULL, --设备功率
    device_speed INTEGER DEFAULT 0 NULL, --设备转速
    position_name VARCHAR(64), -- 部位名称
    position_type VARCHAR(64), -- 部位类型
    bearing_num VARCHAR(64), -- 轴承型号
    gear_count INTEGER DEFAULT 0 NULL, -- 齿轮数量
    component_speed INTEGER DEFAULT 0 NULL, -- 部件转速
    sampling INTEGER DEFAULT 0 NULL, -- 采样点数
    frequency INTEGER DEFAULT 0 NULL, -- 采样频率
    origin_time INTEGER DEFAULT 0 NULL, -- 采样时间

    model_name VARCHAR(255) DEFAULT '' NULL, -- 模型名称
    result VARCHAR(255) DEFAULT '' NULL, -- 诊断结果，默认空字符串
    created_time DATETIME DEFAULT (strftime('%Y-%m-%d %H:%M:%S', 'now')) NULL -- 创建时间，默认当前时间
);


-- 创建机理模型配置表
