import sqlite3

# 1. 打开 MySQL 导出的数据文件
mysql_dump_file = './doc/init_db.sql'
with open(mysql_dump_file, 'r') as f:
    mysql_data = f.read()

# 2. 连接 SQLite 数据库
sqlite_conn = sqlite3.connect('mm_database.db')
sqlite_cursor = sqlite_conn.cursor()

# 3. 清空数据库：删除所有表
sqlite_cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
tables = sqlite_cursor.fetchall()
for table in tables:
    table_name = table[0]
    sqlite_cursor.execute(f"DROP TABLE IF EXISTS {table_name};")
print("数据库已清空！")

# 4. 导入数据到 SQLite
# 假设导出的数据文件中每条记录以 INSERT INTO 开头
for statement in mysql_data.split(';'):
    if statement.strip():
        try:
            sqlite_cursor.execute(statement)
        except Exception as e:
            print(f"执行失败的语句: {statement}\n错误信息: {e}")

# 5. 提交并关闭连接
sqlite_conn.commit()
sqlite_conn.close()

print("数据导入完成！")
