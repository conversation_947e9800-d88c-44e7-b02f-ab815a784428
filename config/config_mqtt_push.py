import configparser
from config.config_env import env_path  # 导入路径变量
import os

def load_mqtt_push_config(env):
    config = configparser.ConfigParser()
    # 拼接配置文件路径
    config_file_path = f'{env_path}config/config.ini'
    # 读取外部配置文件
    if os.path.exists(config_file_path):
        config.read(config_file_path)
    else:
        raise FileNotFoundError(f"Configuration file '{config_file_path}' not found.")

    section_name = env.lower()
    if section_name not in config:
        raise ValueError(f"Environment '{env}' not found in the configuration file.")

    section = config[section_name]

    mqtt_push_host = section.get('mqtt_push_host')
    mqtt_push_port = section.get('mqtt_push_port')
    mqtt_push_username = section.get('mqtt_push_username')
    mqtt_push_password = section.get('mqtt_push_password')
    mqtt_push_client_id = section.get('mqtt_push_client_id')
    mqtt_push_keep_alive = section.get('mqtt_push_keep_alive', fallback=60)  # 默认保活时间 60 秒
    mqtt_push_topic = section.get('mqtt_push_topic')

    return mqtt_push_host, mqtt_push_port, mqtt_push_username, mqtt_push_password, mqtt_push_client_id, mqtt_push_keep_alive, mqtt_push_topic
