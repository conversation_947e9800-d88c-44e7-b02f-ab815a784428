import configparser
from config.config_env import env_path  # 导入路径变量
import os

def load_config_db_path(env):
    config = configparser.ConfigParser()
    # 拼接配置文件路径
    config_file_path = f'{env_path}config/config.ini'
    # 读取外部配置文件
    if os.path.exists(config_file_path):
        config.read(config_file_path)
    else:
        raise FileNotFoundError(f"Configuration file '{config_file_path}' not found.")

    section_name = env.lower()
    if section_name not in config:
        raise ValueError(f"Environment '{env}' not found in the configuration file.")

    section = config[section_name]

    db_path = section.get('db_path')
    print(db_path)

    return db_path

def load_modeltest_config_db_path(env):
    config = configparser.ConfigParser()
    # 拼接配置文件路径
    config_file_path = f'{env_path}config/config.ini'
    # 读取外部配置文件
    if os.path.exists(config_file_path):
        config.read(config_file_path)
    else:
        raise FileNotFoundError(f"Configuration file '{config_file_path}' not found.")

    section_name = env.lower()
    if section_name not in config:
        raise ValueError(f"Environment '{env}' not found in the configuration file.")

    section = config[section_name]

    modeltest_db_path = section.get('modeltest_db_path')
    print(modeltest_db_path)

    return modeltest_db_path
