import configparser
from config.config_env import env_path  # 导入路径变量
import os

def load_config_push(env):
    config = configparser.ConfigParser()
    # 拼接配置文件路径
    config_file_path = f'{env_path}config/config.ini'
    # 读取外部配置文件
    if os.path.exists(config_file_path):
        config.read(config_file_path)
    else:
        raise FileNotFoundError(f"Configuration file '{config_file_path}' not found.")

    section_name = env.lower()
    if section_name not in config:
        raise ValueError(f"Environment '{env}' not found in the configuration file.")

    section = config[section_name]

    mq_push_host = section.get('mq_push_host')
    mq_push_port = section.get('mq_push_port')
    mq_push_username = section.get('mq_push_username')
    mq_push_password = section.get('mq_push_password')
    mq_push_virtual_host = section.get('mq_push_virtual_host')
    mq_push_exchange = section.get('mq_push_exchange')
    mq_push_queue_name = section.get('mq_push_queue_name')
    mq_push_routing_key = section.get('mq_push_routing_key')


    return mq_push_host, mq_push_port, mq_push_username, mq_push_password, mq_push_virtual_host, mq_push_exchange, mq_push_queue_name, mq_push_routing_key
