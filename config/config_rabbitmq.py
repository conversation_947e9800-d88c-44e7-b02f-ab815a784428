import configparser
from config.config_env import env_path  # 导入路径变量
import os

def load_config(env):
    config = configparser.ConfigParser()
    # 拼接配置文件路径
    config_file_path = f'{env_path}config/config.ini'
    # 读取外部配置文件
    if os.path.exists(config_file_path):
        config.read(config_file_path)
    else:
        raise FileNotFoundError(f"Configuration file '{config_file_path}' not found.")

    section_name = env.lower()
    if section_name not in config:
        raise ValueError(f"Environment '{env}' not found in the configuration file.")

    section = config[section_name]

    mq_host = section.get('mq_host')
    mq_port = section.get('mq_port')
    mq_username = section.get('mq_username')
    mq_password = section.get('mq_password')
    mq_virtual_host = section.get('mq_virtual_host')
    mq_exchange = section.get('mq_exchange')
    mq_queue_name = section.get('mq_queue_name')
    mq_routing_key = section.get('mq_routing_key')


    return mq_host, mq_port, mq_username, mq_password, mq_virtual_host, mq_exchange, mq_queue_name, mq_routing_key
