[development]
db_host = localhost
db_port = 3306
db_user = root
db_password = 2Ghlmcl@szyk
db_database = AI

db_path = /Users/<USER>/data/mm/mm_database.db
modeltest_db_path = /Users/<USER>/data/mm/modeltest.sqlite

mqtt_host = 127.0.0.1
mqtt_port = 1883
mqtt_username = hajimi
mqtt_password = Wjd#fj820dIP[RMisX*2jwsDF
mqtt_client_id = mqtt_client_001
mqtt_keep_alive = 60
mqtt_topic = topic/ai/wave

mqtt_push_host = 127.0.0.1
mqtt_push_port = 1883
mqtt_push_username = mqtt
mqtt_push_password = 2Ghlmcl@szyk
mqtt_push_client_id = mqtt_client_001
mqtt_push_keep_alive = 60
mqtt_push_topic = /topic/ai/wave

mq_host = **************
mq_port = 5672
mq_username = szykxm
mq_password = bAuZHNL8#MPlU616
mq_virtual_host = /dev
mq_exchange = exchange.mechanism.model.param
mq_queue_name = queue.mechanism.model.param
mq_routing_key =

mq_push_host = **************
mq_push_port = 5672
mq_push_username = szykxm
mq_push_password = bAuZHNL8#MPlU616
mq_push_virtual_host = /dev
mq_push_exchange = exchange.mechanism.model.alarm
mq_push_queue_name = queue.mechanism.model.alarm
mq_push_routing_key =

[production]
db_host = *************
db_port = 32633
db_user = root
db_password = T#dNs@2eoD4F@j
db_database = sidas_test

db_path = /data/sqlite/mm_database.db

mqtt_host = 127.0.0.1
mqtt_port = 1883
mqtt_username = mqtt
mqtt_password = 2Ghlmcl@szyk
mqtt_client_id = mqtt_client_001
mqtt_keep_alive = 60
mqtt_topic = test

mqtt_push_host = 127.0.0.1
mqtt_push_port = 1883
mqtt_push_username = mqtt
mqtt_push_password = 2Ghlmcl@szyk
mqtt_push_client_id = mqtt_client_001
mqtt_push_keep_alive = 60
mqtt_push_topic = /topic/ai/wave

mq_host = 127.0.0.1
mq_port = 5672
mq_username = hajimi
mq_password = Wjd#fj820dIP[RMisX*2jwsDF
mq_virtual_host = /
mq_exchange = exchange.ai.wave
mq_queue_name = queue.ai.wave
mq_routing_key =

mq_push_host = 127.0.0.1
mq_push_port = 5672
mq_push_username = hajimi
mq_push_password = Wjd#fj820dIP[RMisX*2jwsDF
mq_push_virtual_host = /
mq_push_exchange = ai.diagnosis.exchange.value
mq_push_queue_name = diagnosis.queue.value.ai
mq_push_routing_key =