'''
GF
'''
import numpy as np
import os
import ast
import sys
CURRENT_PATH = os.path.dirname(os.path.realpath(__file__))
sys.path.append(os.path.join(CURRENT_PATH, '..'))
from utils.scipy_fft import fft_plot,envelope_plot
from utils.common_use import get_section_freq_max,get_multi_freq_amplitide,get_max_fvalues_speednX_value
from log.logger import LOG



from mechanism.mm_parent import MM_PARENT


class GEARING_GF(MM_PARENT):
    def __init__(self, args):
        super(GEARING_GF, self).__init__(args)

    # 机理模型诊断
    def diagnosis(self, wave_array):
        x = wave_array
        fs = self.args.fs
        speed = self.args.speed
        gearing_parameter = self.args.gearing_parameter
        model_gearing_GF_para = self.args.model_fault_ratio
        
        if model_gearing_GF_para != -1:
            model_gearing_GF_para = np.linspace(model_gearing_GF_para,1,5).tolist()[:-1]  
        
        gear_set_lines = ast.literal_eval(self.args.model_fault_set_para).get('gear_GF_set_lines',-1)
        gearing_env = ast.literal_eval(self.args.model_fault_set_para).get('gearing_env',-1)
        # print(f'diagnosis gearing para:{gearing_parameter},speed={speed}')
        # print(f'gear_set_lines={gear_set_lines},gear_env={gear_env}')
        time_power = np.sum(np.power(x, 2))/fs
        # print(f'time_power={time_power}')

        gearing_size = len(gearing_parameter)
        #------[{'key':'1','value':[10,1.666}]
        gearing_freq_values = [] # 
        gearing_freq_possibility = [0,0] # 
        # possibility
        gearing_freq_GMF_possibility = np.zeros(gearing_size,dtype=float)
        gearing_freq_GF_possibility = np.zeros(gearing_size,dtype=float)
        if gearing_parameter is None or len(gearing_parameter) == 0:
            return 0,0,[],0,0
        N = len(x)
        deltf = fs / N
        speed_freq = speed/60
        freq_x,real_amp,_a = fft_plot(x, fs)
        freq_x_envelope,real_amp_envelope = envelope_plot(x,fs)
        # gearing_size = len(gearing_parameter)
        GMF = np.zeros(gearing_size,dtype=int)
        GF = np.zeros(gearing_size,dtype=int)
        GMF_level = np.zeros(gearing_size,dtype=int)
        GF_level = np.zeros(gearing_size,dtype=int)
        f_GMF = np.zeros(gearing_size,dtype=float)
        f_GF = np.zeros(gearing_size,dtype=float)
        f_names = ['' for i in range(gearing_size)]
        fault_index = 0
        gearing_freq_possibility = 0
        # for i in range(gearing_size):
        i=0
        for k,v in gearing_parameter.items():
            f_names[i] = k
            f_GF[i] = v[0]/60 #v[0] 20241203
            f_GMF[i] = v[1]
            # print(f'i={i}, k={k},v[0]={v[0]},v[1]={v[1]}')
            i += 1
        gearing_enery_env_all = np.sum((real_amp_envelope/2*N)**2/N/fs*2)#---
        GF,GF_level,fault_index,gearing_freq_possibility,gearing_freq_values_GF,gearing_GF_enery_ratio = gearing_element_calculate(gearing_size,fs,f_GF,real_amp,freq_x,N,deltf,real_amp_envelope,freq_x_envelope,time_power,gearing_enery_env_all,model_gearing_GF_para,gear_set_lines,gearing_env)
        # print(f'GF_level={GF_level}')
        # GMF,GMF_level,fault_index[1],gearing_freq_possibility[1],gearing_freq_values_GMF = gearing_element_calculate(gearing_size,fs,f_GMF,real_amp,freq_x,N,deltf,real_amp_envelope,freq_x_envelope,time_power,gearing_enery_env_all,model_gearing_para,gear_set_lines,gear_env)
        # print(f'GMF_level={GMF_level}')
        # gearing_freq_values.extend(gearing_freq_values_GF)
        # gearing_freq_values.extend(gearing_freq_values_GMF)
        gearing_freq_values = {'齿轮损伤特征频率1X':gearing_freq_values_GF[0],'齿轮损伤特征频率2X':gearing_freq_values_GF[1],'齿轮损伤特征频率3X':gearing_freq_values_GF[2],'"齿轮损伤特征频率4X"':gearing_freq_values_GF[3],'齿轮损伤特征频率包络1X':gearing_freq_values_GF[4],'齿轮损伤特征频率包络2X':gearing_freq_values_GF[5],'齿轮损伤特征频率包络3X':gearing_freq_values_GF[6],'齿轮损伤特征频率包络4X':gearing_freq_values_GF[7]}
        return GF_level,gearing_GF_enery_ratio,gearing_freq_values,gearing_freq_possibility,fault_index



def gearing_element_calculate(bearing_size,fs,f_BPFI,real_amp,freq_x,N,deltf,real_amp_envelope,freq_x_envelope,time_power,bearing_enery_env_all,model_bearing_BPFI_para,BPFI_set_lines,bearing_env):
    bearing_BPFI_enery_array =  np.zeros(bearing_size,dtype=float)
    bearing_BPFI_enery_env_array =  np.zeros(bearing_size,dtype=float)
    bearing_freq_values = []
    fault_index = 0
    bearing_freq_possibility = 0
    for ii in range(bearing_size):#------------------------------------------------------------->>>>----func
        #--20241021---,BPFI_set_lines,model_bearing_BPFI_para-----!!!---bearing_BPFI_enery_temp
        _f_BPFI,max_fvalues_main1X, max_fvalues_main2X, max_fvalues_main3X, max_fvalues_main4X = get_multi_freq_amplitide(fs, f_BPFI[ii],real_amp, freq_x, N, deltf) 
        _f_BPFI_env,max_fvalues_main1X_env, max_fvalues_main2X_env, max_fvalues_main3X_env, max_fvalues_main4X_env = get_multi_freq_amplitide(fs, f_BPFI[ii],real_amp_envelope, freq_x_envelope, N, deltf) 
        bearing_BPFI_enery_temp =  (max_fvalues_main1X /2 *N)**2/N/fs*2 + (max_fvalues_main2X /2 *N)**2/N/fs*2 + (max_fvalues_main3X /2 *N)**2/N/fs*2 + (max_fvalues_main4X /2 *N)**2/N/fs*2
        # print(f'before set line: bearing_BPFI_enery_temp={bearing_BPFI_enery_temp}')
        bearing_BPFI_enery_env =  (max_fvalues_main1X_env /2 *N)**2/N/fs*2 + (max_fvalues_main2X_env /2 *N)**2/N/fs*2 + (max_fvalues_main3X_env /2 *N)**2/N/fs*2 + (max_fvalues_main4X_env /2 *N)**2/N/fs*2
        if BPFI_set_lines !=-1 and BPFI_set_lines > 4:
            # print(f'set more lines to BPFI_lines! set_lines={BPFI_set_lines}')
            for bpfi_i in range(5,BPFI_set_lines+1):
                # print(f'bpfi_i={bpfi_i}')
                BPFI_iX = _f_BPFI * bpfi_i
                #check 20241021,main_nX > fs/2.56
                if BPFI_iX > fs/2.56:
                    # print(f'BPFI_iX频率设置溢出采集范围:break loose_i!')
                    break
                max_fvalues_BPFIiX = get_max_fvalues_speednX_value(BPFI_iX,deltf,N,fs,real_amp,freq_x)
                BPFI_enery_i_temp = (max_fvalues_BPFIiX /2 *N)**2/N/fs*2
                # print(f'BPFI_enery_i_temp={BPFI_enery_i_temp}')
                #计算>4 loose lines enery sum；+ 1~4x；
                bearing_BPFI_enery_temp += BPFI_enery_i_temp
        # print(f'after setlines: bearing_BPFI_enery_temp={bearing_BPFI_enery_temp}')
        bearing_BPFI_enery_array[ii] = bearing_BPFI_enery_temp
        bearing_BPFI_enery_env_array[ii] = bearing_BPFI_enery_env
        bearing_freq_values.append([max_fvalues_main1X,max_fvalues_main2X,max_fvalues_main3X,max_fvalues_main4X,max_fvalues_main1X_env,max_fvalues_main2X_env,max_fvalues_main3X_env,max_fvalues_main4X_env])
    # print(f'------------求解所有轴承内圈得到的能量和----------')
    
    bearing_BPFI_enery_ratio = np.sum(bearing_BPFI_enery_array) / time_power
    bearing_BPFI_enery_env_ratio = np.sum(bearing_BPFI_enery_env_array) / bearing_enery_env_all
    # print(f'gear per ratio= {bearing_BPFI_enery_ratio} VS {bearing_BPFI_enery_env_ratio}')
    if model_bearing_BPFI_para != -1:
        res = 0
        while res < len(model_bearing_BPFI_para) and bearing_BPFI_enery_ratio >= model_bearing_BPFI_para[res] :
            res += 1
        print(bearing_BPFI_enery_ratio, res,model_bearing_BPFI_para)
        bearing_BPFI_level = res
        if res == 0:
            bearing_BPFI = 0
            # fault_index.append(0)
            fault_index = 0
            # print(f'包络:test---bearing_env:4')
            if bearing_env != -1:
                # print('设置了包络比较')
                if bearing_BPFI_enery_env_ratio > model_bearing_BPFI_para[0]:
                    # print(f'超出设置:bearing_BPFI_enery_env_ratio={bearing_BPFI_enery_env_ratio},model_bearing_BPFI_para[0]={model_bearing_BPFI_para[0]}')
                    bearing_BPFI_level = 1
                    bearing_BPFI = 1
                    # fault_index.append(np.argmax(bearing_BPFI_enery_env_array) + 1)
                    fault_index = np.argmax(bearing_BPFI_enery_env_array) + 1
                    bearing_freq_possibility = 0.6 + 0.4/(1 - model_bearing_BPFI_para[0])*(bearing_BPFI_enery_env_ratio-model_bearing_BPFI_para[0])
        else:
            # fault_index.append(np.argmax(bearing_BPFI_enery_array) + 1)
            fault_index = np.argmax(bearing_BPFI_enery_array) + 1
            bearing_BPFI = 1
            bearing_freq_possibility = 0.6 + 0.4/(1 - model_bearing_BPFI_para[0])*(bearing_BPFI_enery_ratio-model_bearing_BPFI_para[0])
            # print(f' BPFI, bearing_freq_possibility={bearing_freq_possibility}')
    else:
        bearing_BPFI = 0 
        bearing_BPFI_level = 0
    # print(f'bearing_BPFI={bearing_BPFI},bearing_BPFI_level={bearing_BPFI_level},fault_index={fault_index},bearing_freq_possibility={bearing_freq_possibility}')
    return bearing_BPFI,bearing_BPFI_level,fault_index,bearing_freq_possibility,bearing_freq_values,bearing_BPFI_enery_ratio



