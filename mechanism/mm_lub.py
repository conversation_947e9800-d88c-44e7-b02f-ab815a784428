import os
import sys
import numpy as np
CURRENT_PATH = os.path.dirname(os.path.realpath(__file__))
sys.path.append(os.path.join(CURRENT_PATH, '..'))
from utils.lubrication_feature import feature_extract_freq_env
from mechanism.mm_parent import MM_PARENT
from log.logger import LOG


# 润滑故障
class LUB(MM_PARENT):
    def __init__(self, args):
        super(LUB, self).__init__(args)


    # 机理模型诊断
    def diagnosis(self, wave_array):
        lub_freq_values = 0 # 
        lub_freq_possibility = 0 # 
        lub = 0
        lub_level = 0

        model_lub_para = self.args.model_fault_ratio #self.args.get('lub',-1)
        fs = self.args.fs
        lub_zoom = 20 #self.args.lub_zoom
        if model_lub_para != -1:
            if fs > 10000:
                scale_s = (fs/2.56-2000)/1000
                # model_lub_para = np.linspace(200*model_lub_para,200,5).tolist()[:-1]#100
            else:
                scale_s = fs/2.56/2/1000
                # model_lub_para = np.linspace(20*model_lub_para,20,5).tolist()[:-1]#10
            model_lub_para = np.linspace(lub_zoom*model_lub_para,lub_zoom,5).tolist()[:-1]

        lub_absolute_value = model_lub_para #self.args.lub_freq_para

        if lub_absolute_value != -1:
            
            area_S_down,area_up_down = feature_extract_freq_env(wave_array, self.args.fs)
            area_S_down /= scale_s
            LOG.info(f'lub_enery_ratio={area_S_down},scale_s={scale_s},area_up_down = {area_up_down}')
            res = 0
            while res < len(lub_absolute_value) and area_S_down >= lub_absolute_value[res]:
                res += 1
            lub_level = res
            # lub_freq_values = area_S_down
            # lub_freq_values = area_up_down
            lub_freq_values = {'润滑面积':area_up_down}
            if area_S_down >=lub_zoom:
                lub_freq_possibility = 1
            else:
                lub_freq_possibility = 0.6 + 0.4/(lub_zoom - lub_absolute_value[0])*(area_S_down-lub_absolute_value[0])
            # if self.args.fs > 10000:
            #     if area_S_down >=200:
            #         lub_freq_possibility = 1
            #     else:
            #         lub_freq_possibility = 0.6 + 0.4/(200 - lub_absolute_value[0])*(area_S_down-lub_absolute_value[0])
            # else:
            #     if area_S_down >=20:
            #         lub_freq_possibility = 1
            #     else:
            #         lub_freq_possibility = 0.6 + 0.4/(20 - lub_absolute_value[0])*(area_S_down-lub_absolute_value[0])
            # if res == 0:
            #     lub = 0
            # else:
            #     lub = 1
        else:
            lub = 0
            lub_level = 0
        return lub_level,area_S_down,lub_freq_values,lub_freq_possibility