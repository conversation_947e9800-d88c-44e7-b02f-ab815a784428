'''
diagnose_rotor_eccentricity
date:20250630,bend
'''
import numpy as np
import os
import sys
CURRENT_PATH = os.path.dirname(os.path.realpath(__file__))
sys.path.append(os.path.join(CURRENT_PATH, '..'))
from utils.scipy_fft import fft_plot
from utils.common_use import get_section_freq_max,get_max_fvalues_speednX_value # 
from log.logger import LOG


from mechanism.mm_parent import MM_PARENT


class BEND(MM_PARENT):
    def __init__(self, args):
        super(BEND, self).__init__(args)

    def _detect_harmonics(self, freqs, psd, base_freq, max_harmonic=5):
        """检测基频的谐波成分"""
        harmonic_amplitudes = []
        for i in range(1, max_harmonic+1):
            freq = base_freq * i
            idx = np.argmin(np.abs(freqs - freq))
            harmonic_amplitudes.append(psd[idx])
        return np.array(harmonic_amplitudes)
    
    # 机理模型诊断
    def diagnosis(self, wave_array):
        x = wave_array
        # LOG.info(f'wave_array={wave_array}')
        fs = self.args.fs
        speed = self.args.speed
        LOG.info(f'fs={fs},speed={speed}')
        # model_unbalance_para = self.args.get('unbalance',-1)
        model_bend_para = self.args.model_fault_ratio 
        LOG.info(f'model_bend_para={model_bend_para}')
        if model_bend_para != -1:
            model_bend_para = np.linspace(model_bend_para,1,5).tolist()[:-1]  
        time_power = np.sum(np.power(x, 2))/fs
        # print(f'time_power={time_power}')
        bend = 0
        bend_level = 0
        speed_freq_possibility = 0
        N = len(x)
        deltf = fs / N
        speed_freq = speed/60
        freqs,psd,_angle = fft_plot(x, fs)
        # 检测1倍频、2倍频和3倍频幅值
        harmonic_amps = self._detect_harmonics(freqs, psd, speed_freq, max_harmonic=3)
        fundamental_amp, second_harmonic_amp, third_harmonic_amp = harmonic_amps
        total_amp = np.sum(np.power(x, 2))/fs
        
        # 计算谐波比值
        fundamental_ratio = fundamental_amp / total_amp
        second_harmonic_ratio = second_harmonic_amp / fundamental_amp
        third_harmonic_ratio = third_harmonic_amp / fundamental_amp
        
        # 判断准则
        is_bent = (fundamental_ratio >0.3 and second_harmonic_ratio > 0.5 and  # 2倍频幅值超过1倍频的50%
                   third_harmonic_ratio < 0.3 and third_harmonic_ratio >0.1 )       # 3倍频幅值较小

        bend_enery = (fundamental_amp /2 *N)**2/N/fs*2 + (second_harmonic_amp /2 *N)**2/N/fs*2 + (third_harmonic_amp /2 *N)**2/N/fs*2
        print(f'bend_enery:{bend_enery}')
        bend_enery_ratio = bend_enery/time_power
        LOG.info(f'bend_enery_ratio={bend_enery_ratio}')
        if model_bend_para != -1 and is_bent:
            res = 0
            while res < len(model_bend_para) and bend_enery_ratio >= model_bend_para[res]:
                res += 1
            bend_level = res
            LOG.info(bend_enery_ratio, res,model_bend_para)
            if res == 0:
                bend = 0
            else:
                bend = 1
        else:
            bend = 0
            bend_level = 0
        speed_freq_values = {'转频1X':fundamental_amp,'转频2X':second_harmonic_amp,'转频3X':third_harmonic_amp}
        if bend:
            speed_freq_possibility = 0.6 + 0.4/(1 - model_bend_para[0])*(bend_enery_ratio-model_bend_para[0]) #
        
        return bend_level,bend_enery_ratio,speed_freq_values,speed_freq_possibility
