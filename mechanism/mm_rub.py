# date:20250716
import numpy as np
import os
import sys
import ast
CURRENT_PATH = os.path.dirname(os.path.realpath(__file__))
sys.path.append(os.path.join(CURRENT_PATH, '..'))
# from scipy.fftpack import fft 
from scipy.fft import fft, fftfreq
from mechanism.mm_parent import MM_PARENT
from log.logger import LOG

# 摩擦故障
class RUB(MM_PARENT):
    def __init__(self, args):
        super(RUB, self).__init__(args)


    # 机理模型诊断
    def diagnosis(self, wave_array):
        model_rub_para = self.args.model_fault_ratio #self.args.get('rub',-1) 
        if model_rub_para != -1:
            model_rub_para = np.linspace(model_rub_para,1,5).tolist()[:-1]  
        rub_freq_quarter_3X_per = model_rub_para
        rub_set_lines = ast.literal_eval(self.args.model_fault_set_para).get('rub_structure_lines',-1)
        LOG.info(f'model_rub_para={model_rub_para},rub_set_lines={rub_set_lines}')
        per_rub = 0
        rub_freq_values = 0 # 
        rub_freq_possibility = 0 # 
        N = len(wave_array)
        # rubs = 0
        rubs_level = 0
        # deltf = fs / N
        speed_1X = self.args.speed/60
        shaft_speed_hz = speed_1X
        # 修正转频
        fs = self.args.fs
        # deltf = fs / N
        freqs = fftfreq(N, 1/fs)[:N//2]
        fft_signal = 2*np.abs(fft(wave_array))[:N//2]/N
        local_range = 2  # ±2Hz范围
        LOG.info(f'转频修正前shaft_speed_hz:{shaft_speed_hz}')
        freq_mask = (freqs >= shaft_speed_hz - local_range) & (freqs <= shaft_speed_hz + local_range) # 设定频率误差范围
        if np.any(freq_mask):
            peak_idx = np.argmax(fft_signal[freq_mask])
            actual_freq = freqs[freq_mask][peak_idx]
            base_amplitude = fft_signal[freq_mask][peak_idx]
            LOG.info(f'actual_freq={actual_freq},base_amplitude={base_amplitude}')
            shaft_speed_hz = actual_freq
            LOG.info(f'转频修正后shaft_speed_hz:{shaft_speed_hz}')
        #------计算摩擦特征频率------
        fractional_harmonics = [0.5 * shaft_speed_hz, 1.5 * shaft_speed_hz, 2.5 * shaft_speed_hz]
        if rub_set_lines != -1:
            integer_harmonics = [i * shaft_speed_hz for i in range(2, rub_set_lines+1)]  # 2-5次谐波
        else:
            integer_harmonics = [i * shaft_speed_hz for i in range(2, 6)]  # 2-5次谐波
        # 检测分数谐波
        fractional_peaks = []
        for freq in fractional_harmonics:
            freq_mask = (freqs >= freq - 1.5) & (freqs <= freq + 1.5) # 设定频率误差范围
            if np.any(freq_mask):
                peak_idx = np.argmax(fft_signal[freq_mask])
                actual_freq = freqs[freq_mask][peak_idx]
                amplitude = fft_signal[freq_mask][peak_idx]
                fractional_peaks.append({
                    'expected_freq': freq,
                    'actual_freq': actual_freq,
                    'amplitude': amplitude
                })
        # 检测整数谐波
        integer_peaks = []
        for freq in integer_harmonics:
            freq_mask = (freqs >= freq - 1.5) & (freqs <= freq + 1.5)
            if np.any(freq_mask):
                peak_idx = np.argmax(fft_signal[freq_mask])
                actual_freq = freqs[freq_mask][peak_idx]
                amplitude = fft_signal[freq_mask][peak_idx]
                integer_peaks.append({
                    'expected_freq': freq,
                    'actual_freq': actual_freq,
                    'amplitude': amplitude
                })
        # 验证基频有效性
        base_freq_idx = np.argmin(np.abs(freqs - shaft_speed_hz))
        base_amplitude = fft_signal[base_freq_idx]
        LOG.info(f'base_amplitude={base_amplitude}')
        if  base_amplitude < 0.3:
            LOG.info(f'基频幅值较低,基频无效!')
            return 0,0,{'摩擦能量':0},0
        freq_nengliang = 0 # 分数次谐波和整数次谐波能量累加和
        significant_fractional_peaks = 0
        for peak in fractional_peaks:
            freq_nengliang += (peak['amplitude'] /2 *N)**2/N/fs*2  # (peak['amplitude'] /2 *N)**2/N/fs*2
            # 检查幅值是否显著（相对于基频幅值的比例）
            if peak['amplitude'] > 0.15 * base_amplitude:  # 幅值阈值：基频的10%->15%
                significant_fractional_peaks += 1
        if significant_fractional_peaks >= 2:
            LOG.info(f'检测到{significant_fractional_peaks}个显著分数谐波')
            rub_freq_possibility += 0.4
        # 检查高次谐波 - 使用相同的基频验证
        significant_integer_peaks = 0
        for peak in integer_peaks:
            freq_nengliang += (peak['amplitude'] /2 *N)**2/N/fs*2
            # 检查幅值是否显著（相对于基频幅值的比例）
            if peak['amplitude'] > 0.3 * base_amplitude:  # 幅值阈值：基频的15%->30%
                significant_integer_peaks += 1
        if significant_integer_peaks >= 3:
            LOG.info(f'检测到{significant_integer_peaks}个显著高次谐波')
            rub_freq_possibility += 0.2
        rms = np.sqrt(np.mean(wave_array**2))
        # 峭度
        kurtosis = np.mean((wave_array - np.mean(wave_array))**4) / (np.std(wave_array)**4)
        # 偏度 - 摩擦可能导致波形不对称
        skewness = np.mean((wave_array - np.mean(wave_array))**3) / (np.std(wave_array)**3)
        # 检查峭度值 - 摩擦通常导致高峭度
        if kurtosis > 4:
            LOG.info(f'高峭度值 kurtosis:{kurtosis}')
            rub_freq_possibility += 0.2
        # 检查偏度 - 摩擦可能导致波形不对称
        if skewness > 0.5:
            LOG.info(f'波形不对称，偏度:{skewness}')
            rub_freq_possibility += 0.2

        if rub_freq_quarter_3X_per != -1 : #and rub_freq_possibility >= 0.6
            x_nengliang = np.sum(np.power(wave_array, 2))/self.args.fs # /fs
            per_rub = freq_nengliang / x_nengliang
            LOG.info(f'rub_enery_ratio={per_rub},freq_nengliang={freq_nengliang},x_nengliang={x_nengliang}')
            res = 0
            while res < len(rub_freq_quarter_3X_per) and per_rub >= rub_freq_quarter_3X_per[res]:
                res += 1
            rubs_level = res
            rub_freq_values = {'摩擦能量':freq_nengliang}
        else:
            rubs_level = 0
        return rubs_level,per_rub,rub_freq_values,rub_freq_possibility