'''
电流故障诊断测试:20250214
stator_eccentric 定子偏心 STATORECCENTRIC
'''
import numpy as np
import os
import sys
import ast
import pywt  # 添加这行
from scipy import signal
from scipy.fft import fft
CURRENT_PATH = os.path.dirname(os.path.realpath(__file__))
sys.path.append(os.path.join(CURRENT_PATH, '..'))
import warnings
warnings.filterwarnings('ignore')   
from log.logger import LOG
from mechanism.mm_parent import MM_PARENT


class STATORECCENTRIC(MM_PARENT):
    def __init__(self, args):   #
        super(STATORECCENTRIC, self).__init__(args)
        self.fs = self.args.fs  # 采样频率sampling_rate
        self.power_freq = 50  # 电源频率(Hz)
        self.p = self.args.p # 级数 
        self.rotor_speed = self.args.speed

    def fft_analysis(self, signal):
        """FFT频谱分析"""
        N = len(signal)
        freq = np.fft.fftfreq(N, 1/self.fs)
        fft_result = 2*np.abs(fft(signal))/N   # np.abs(fft(signal)) 
        return freq[:N//2], fft_result[:N//2]
  
    
    def diagnosis(self,current):#
        """定子偏心故障诊断"""
        freq, spectrum = self.fft_analysis(current)
        time_power = np.sum(np.power(current, 2))/self.fs
        fr = self.rotor_speed / 60  # 转子频率
        N = len(current)
        deltf = self.fs/N
        stator_eccentric_possibility = 0
        model_para = self.args.model_fault_ratio 
        if model_para != -1:
            model_para = np.linspace(model_para,1,5).tolist()[:-1]  
        # loose_set_lines = ast.literal_eval(self.args.model_fault_set_para).get('loose_structure_lines',-1)
        # 特征频率 fs±fr
        feature_freqs = [self.power_freq + fr, self.power_freq - fr]
        fault_indicators = []
        for target_freq in feature_freqs:
            mask = (freq >= target_freq - 3*deltf) & (freq <= target_freq + 3*deltf)
            peak_amp = np.max(spectrum[mask])
            fault_indicators.append(peak_amp)
        LOG.info(f'feature_freqs={feature_freqs},fault_indicators={fault_indicators}')
        _enery = 0 #
        for broken_i in fault_indicators:
            print(f'broken_i={broken_i}')
            _enery += (broken_i /2 *self.N)**2/self.N/self.fs*2
        LOG.info(f'_enery={_enery}')

        stator_eccentric_enery_ratio = _enery/time_power
        LOG.info(f'stator_eccentric_enery_ratio={stator_eccentric_enery_ratio}')
        if model_para !=-1 :#
            res = 0
            while res < len(model_para) and stator_eccentric_enery_ratio >= model_para[res] :
                res += 1
            stator_eccentric_level = res
        else:
            stator_eccentric_level = 0
        # stator_eccentric_freq_values = fault_indicators
        stator_eccentric_freq_values = {'定子偏心':fault_indicators}
        if stator_eccentric_level:
            stator_eccentric_possibility = 0.6 + 0.4/(1-model_para[0])*(stator_eccentric_enery_ratio-model_para[0])
        return stator_eccentric_level,stator_eccentric_enery_ratio,stator_eccentric_freq_values,stator_eccentric_possibility
    

