'''
螺杆式压缩机转子磨损

'''
import numpy as np
from scipy import signal
import os
import ast
import sys
CURRENT_PATH = os.path.dirname(os.path.realpath(__file__))
sys.path.append(os.path.join(CURRENT_PATH, '..'))
from log.logger import LOG
# from utils.scipy_fft import fft_plot,envelope_plot


class CompressorWearDetector:
    def __init__(self,args):
        super(CompressorWearDetector, self).__init__(args)
        """
        螺杆式压缩机转子磨损诊断器
        """
    
    def diagnose_wear(self, vibration_data, sampling_rate):
        """
        分析转子磨损特征
        :param vibration_data: 振动信号数组
        :return: (是否磨损, 特征频率, 谐波能量比)
        """
        speed_freq = self.args.speed / 60
        model_fault_ratio1 = self.args.model_fault_ratio #
        if model_fault_ratio1 != -1:
            model_fault_ratio1 = np.linspace(model_fault_ratio1,1,5).tolist()[:-1]
        loose_set_lines = ast.literal_eval(self.args.model_fault_set_para).get('loose_structure_lines',-1)

        wear_freq_range = np.arange(0.5,loose_set_lines+1,0.5)  #生成分数次谐波
        # 计算FFT频谱
        deltf = sampling_rate / len(vibration_data)
        fft_data = np.fft.fft(vibration_data)
        freqs = np.fft.fftfreq(len(vibration_data), 1/sampling_rate)
        amplitudes = np.abs(fft_data[:len(freqs)//2])
        freqs = freqs[:len(freqs)//2]

        # 计算特征频率范围内的能量
        wear_energy_sum = 0
        for i in wear_freq_range:
            cent_f = speed_freq * i
            wear_mask = (freqs >= cent_f-3* deltf) & (freqs <= cent_f+3*deltf)
            wear_energy_temp = np.sum(amplitudes[wear_mask])
            LOG.info(f'i ={i}, wear_energy_temp={wear_energy_temp}')
            wear_energy_sum += wear_energy_temp
        total_energy = np.sum(amplitudes)
        wear_ratio = wear_energy_sum / total_energy
        LOG.info(f'deltf = {deltf}, loose_set_lines = {loose_set_lines},wear_ratio={wear_ratio},wear_energy_sum={wear_energy_sum}')
        #------
        wear_possibility = 0
        if model_fault_ratio1 !=-1 :#
            res = 0
            while res < len(model_fault_ratio1) and wear_ratio >= model_fault_ratio1[res] :
                res += 1
            wear_level = res
        else:
            wear_level = 0
        if wear_level:
            wear_possibility = 0.6 + 0.4/(1-model_fault_ratio1[0])*(wear_ratio-model_fault_ratio1[0])
        # 
        wear_values = 2* wear_energy_sum /len(vibration_data)

        return wear_level,wear_ratio,wear_values,wear_possibility
        

#---螺杆压缩机转子磨损----
class SCREW_COMPRESSOR:
    def __init__(self):
        self.fault_types = {
            "WEAR": "磨损"
        }
    
    @classmethod
    def diagnosis(cls, args):
        fs = args.fs
        vibration_data = args.wave_array
        result = []
        # 创建诊断实例
        vfd = CompressorWearDetector(args) #
        LOG.info(f'args.model_type={args.model_type}') 
        # 执行各类故障检测
        if args.model_type == 'WEAR':
            result = vfd.diagnose_wear(vibration_data, fs) #diagnose_wear

        LOG.info(f'result={result}')
        return result


if __name__ == "__main__":
    pass