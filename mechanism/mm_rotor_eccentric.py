'''
电流故障诊断测试:20250214

rotor_eccentric 转子偏心 ROTORECCENTRIC

'''
import numpy as np
import os
import sys
import ast
import pywt
from scipy import signal
from scipy.fft import fft
CURRENT_PATH = os.path.dirname(os.path.realpath(__file__))
sys.path.append(os.path.join(CURRENT_PATH, '..'))
import warnings
warnings.filterwarnings('ignore')   
from log.logger import LOG
from mechanism.mm_parent import MM_PARENT


class ROTORECCENTRIC(MM_PARENT):
    def __init__(self, args):   #
        super(ROTORECCENTRIC, self).__init__(args)
        self.fs = self.args.fs  # 采样频率sampling_rate
        self.power_freq = 50  # 电源频率(Hz)
        self.p = self.args.p # 级数 
        self.rotor_speed = self.args.speed
        self.rotor_slots = self.args.rotor_slots

    def fft_analysis(self, signal):
        """FFT频谱分析"""
        N = len(signal)
        freq = np.fft.fftfreq(N, 1/self.fs)
        fft_result = 2*np.abs(fft(signal))/N   # np.abs(fft(signal)) 
        return freq[:N//2], fft_result[:N//2]
    

    def diagnosis(self,current):
        """转子偏心故障诊断"""
        freq, spectrum = self.fft_analysis(self.current)
        time_power = np.sum(np.power(current, 2))/self.fs
        N = len(current)
        fr = self.rotor_speed / 60
        deltf = self.fs/N
        rotor_eccentric_possibility = 0
        model_para = self.args.model_fault_ratio 
        if model_para != -1:
            model_para = np.linspace(model_para,1,5).tolist()[:-1]  
        # loose_set_lines = ast.literal_eval(self.args.model_fault_set_para).get('loose_structure_lines',-1)
        # 转子槽谐波频率
        rsf = self.rotor_slots * fr
        feature_freqs = [
            self.power_freq + fr,  # 静态偏心
            self.power_freq + rsf  # 动态偏心
        ]
        fault_indicators = []
        for target_freq in feature_freqs:
            if target_freq < self.fs/2.56:
                mask = (freq >= target_freq - 3*deltf) & (freq <= target_freq + 3*deltf)
                peak_amp = np.max(spectrum[mask])
                fault_indicators.append(peak_amp)
        _enery = 0 #
        for broken_i in fault_indicators:
            print(f'broken_i={broken_i}')
            _enery += (broken_i /2 *self.N)**2/self.N/self.fs*2
        print(f'_enery={_enery}')

        rotor_eccentric_enery_ratio = _enery/time_power
        LOG.info(f'rotor_eccentric_enery_ratio={rotor_eccentric_enery_ratio}')  
        if model_para !=-1 :#
            res = 0
            while res < len(model_para) and rotor_eccentric_enery_ratio >= model_para[res] :
                res += 1
            rotor_eccentric_level = res
        else:
            rotor_eccentric_level = 0
        # rotor_eccentric_freq_values = fault_indicators
        rotor_eccentric_freq_values = {'转子偏心':fault_indicators}
        if rotor_eccentric_level:
            rotor_eccentric_possibility = 0.6 + 0.4/(1-model_para[0])*(rotor_eccentric_enery_ratio-model_para[0])   
        
        return rotor_eccentric_level,rotor_eccentric_enery_ratio,rotor_eccentric_freq_values,rotor_eccentric_possibility

