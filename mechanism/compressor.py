'''
离心压缩机喘振:
'''

# import numpy as np
# import matplotlib.pyplot as plt
# from scipy.signal import find_peaks
import numpy as np
from scipy import signal
import os
import ast
import sys
CURRENT_PATH = os.path.dirname(os.path.realpath(__file__))
sys.path.append(os.path.join(CURRENT_PATH, '..'))
import matplotlib
matplotlib.use('TkAgg')
import matplotlib.pyplot as plt
plt.rcParams['font.sans-serif']=['SimHei'] #用来正常显示中文标签
plt.rcParams['axes.unicode_minus']=False #用来正常显示负号
from log.logger import LOG
# from utils.scipy_fft import fft_plot,envelope_plot


"""
初始化喘振检测器
:param sampling_rate: 采样频率 (Hz)
"""
class CompressorSurgeDetector:
    def __init__(self,args):
        super(CompressorSurgeDetector, self).__init__(args)
        # self.sampling_rate = sampling_rate
        # self.surge_threshold = 0.5  # 喘振特征能量阈值
        # self.surge_freq_range = (30, 100)  # 喘振特征频率范围(Hz)

    def diagnose_surge(self, vibration_data, sampling_rate):
        """
        检测喘振故障
        :param vibration_data: 振动信号数组
        :return: (是否发生喘振, 特征频率, 特征能量比)
        """
        model_fault_ratio1 = self.args.model_fault_ratio #
        if model_fault_ratio1 != -1:
            model_fault_ratio1 = np.linspace(model_fault_ratio1,1,5).tolist()[:-1] 
        
        resonance_band_left = ast.literal_eval(self.args.model_fault_set_para).get('resonance_band_left',-1)  #
        resonance_band_right = ast.literal_eval(self.args.model_fault_set_para).get('resonance_band_right',-1) #
        #----喘振特征频率范围(Hz)设置----
        surge_freq_range = (resonance_band_left, resonance_band_right)
        # 计算FFT频谱
        fft_data = np.fft.fft(vibration_data)
        freqs = np.fft.fftfreq(len(vibration_data), 1/sampling_rate)
        amplitudes = np.abs(fft_data[:len(freqs)//2])
        freqs = freqs[:len(freqs)//2]

        # 计算特征频率范围内的能量
        surge_mask = (freqs >= surge_freq_range[0]) & (freqs <= surge_freq_range[1])
        surge_energy = np.sum(amplitudes[surge_mask])
        total_energy = np.sum(amplitudes)
        surge_ratio = surge_energy / total_energy
        LOG.info(f'surge_ratio={surge_ratio}')
        #------
        surge_possibility = 0
        if model_fault_ratio1 !=-1 :#
            res = 0
            while res < len(model_fault_ratio1) and surge_ratio >= model_fault_ratio1[res] :
                res += 1
            surge_level = res
        else:
            surge_level = 0
        if surge_level:
            surge_possibility = 0.6 + 0.4/(1-model_fault_ratio1[0])*(surge_ratio-model_fault_ratio1[0])
        # 
        surge_values = 2* surge_energy /len(vibration_data)
        return surge_level,surge_ratio,surge_values,surge_possibility

    def plot_diagnosis(self, vibration_data, time):
        """
        绘制诊断结果图
        :param vibration_data: 振动信号数组
        :param time: 时间数组
        """
        has_surge, peak_freqs, energy_ratio = self.detect_surge(vibration_data)
        
        plt.figure(figsize=(12, 6))
        
        # 时域图
        plt.subplot(2, 1, 1)
        plt.plot(time, vibration_data)
        plt.title(f'振动信号 (喘振: {"是" if has_surge else "否"}, 能量比: {energy_ratio:.2f})')
        plt.xlabel('时间 (s)')
        plt.ylabel('振幅')
        
        # 频域图
        plt.subplot(2, 1, 2)
        fft_data = np.fft.fft(vibration_data)
        freqs = np.fft.fftfreq(len(vibration_data), 1/self.sampling_rate)
        amplitudes = np.abs(fft_data[:len(freqs)//2])
        freqs = freqs[:len(freqs)//2]
        
        plt.plot(freqs, amplitudes)
        plt.axvspan(self.surge_freq_range[0], self.surge_freq_range[1], 
                   color='red', alpha=0.2, label='喘振特征频带')
        plt.title('频谱分析')
        plt.xlabel('频率 (Hz)')
        plt.ylabel('振幅')
        plt.legend()
        
        plt.tight_layout()
        plt.show()


#---离心压缩机喘振----
class COMPRESSOR:
    def __init__(self):
        self.fault_types = {
            "SURGE": "喘振"
        }
    
    @classmethod
    def diagnosis(cls, args):
        fs = args.fs
        vibration_data = args.wave_array

        result = []
        
        # 创建诊断实例
        vfd = CompressorSurgeDetector(args) #
        LOG.info(f'args.model_type={args.model_type}') 
        # 执行各类故障检测
        if args.model_type == 'SURGE':
            result = vfd.diagnose_surge(vibration_data, fs) #diagnose_surge

        LOG.info(f'result={result}')
        return result


if __name__ == "__main__":
    pass
    # # 示例使用
    # sampling_rate = 1000
    # t = np.linspace(0, 1, sampling_rate)
    
    # # 模拟正常振动信号
    # normal_vib = 0.5 * np.sin(2 * np.pi * 50 * t)
    
    # # 模拟喘振振动信号 (包含低频波动)
    # surge_vib = normal_vib + 1.2 * np.sin(2 * np.pi * 40 * t) + 0.8 * np.sin(2 * np.pi * 70 * t)
    
    # # 创建检测器
    # detector = CompressorSurgeDetector(sampling_rate)
    
    # # 分析正常信号
    # print("正常信号分析:")
    # detector.plot_diagnosis(normal_vib, t)
    
    # # 分析喘振信号
    # print("\n喘振信号分析:")
    # detector.plot_diagnosis(surge_vib, t)