'''
blade:
'''
import numpy as np
import os
import ast
import sys
CURRENT_PATH = os.path.dirname(os.path.realpath(__file__))
sys.path.append(os.path.join(CURRENT_PATH, '..'))
from utils.scipy_fft import fft_plot,envelope_plot
from utils.common_use import get_section_freq_max,get_multi_freq_amplitide,get_max_fvalues_speednX_value


from mechanism.mm_parent import MM_PARENT

class FAN(MM_PARENT):
    def __init__(self, args):
        super(FAN, self).__init__(args)

    # 机理模型诊断
    def diagnosis(self, wave_array):
        x = wave_array
        fs = self.args.fs
        speed = self.args.speed
        fan_number = self.args.fan_number
        model_blade_para = self.args.model_fault_ratio 
        if model_blade_para != -1:
            model_blade_para = np.linspace(model_blade_para,1,5).tolist()[:-1]  
        
        fan_set_lines = ast.literal_eval(self.args.model_fault_set_para).get('fan_set_lines',-1)

        print(f'diagnosis_blade:speed={speed},fan_set_lines={fan_set_lines}')
        time_power = np.sum(np.power(wave_array, 2))/fs
        print(f'time_power={time_power}')
        fan_size = len(fan_number)
        blade_freq_values = [] # 
        blade_freq_possibility = 0 # 
        # blade_freq_YP_possibility = np.zeros(fan_size,dtype=float)
        if fan_number is None or len(fan_number)==0:
            return 0,0,[],[],0
        fault_index_fan =[]
        # fan_freq_1X = fan_structure_list[0]
        # fan_freq_2X = fan_structure_list[1]
        # fan_freq_3X = fan_structure_list[2]
        # fan_freq_4X = fan_structure_list[3]
        N = len(wave_array)
        deltf = fs / N
        speed_freq = speed/60
        freq_x,real_amp,_a = fft_plot(wave_array, fs)
        # freq_x_envelope,real_amp_envelope = envelope_plot(wave_array,fs)
        # fan_size = len(fan_number)
        fan = np.zeros(fan_size,dtype=int)
        fan_level = np.zeros(fan_size,dtype=int)
        f_fan = speed_freq * np.array(fan_number) #np.zeros(fan_size,dtype=float)
        fan,fan_level,fault_index_fan,blade_freq_possibility,blade_freq_values,fan_enery_ratio = fan_element_calculate(fan_size,fs,f_fan,real_amp,freq_x,N,deltf,time_power,model_blade_para,fan_set_lines)
        blade_freq_possibility =  {"叶片1X":blade_freq_possibility[0],"叶片1X":blade_freq_possibility[1],"叶片1X":blade_freq_possibility[2],"叶片1X":blade_freq_possibility[3]}
        return fan_level,fan_enery_ratio,blade_freq_values,blade_freq_possibility,fault_index_fan
    
    
def fan_element_calculate(bearing_size,fs,f_BPFI,real_amp,freq_x,N,deltf,time_power,model_bearing_BPFI_para,BPFI_set_lines):
    bearing_BPFI_enery_array =  np.zeros(bearing_size,dtype=float)
    # bearing_BPFI_enery_env_array =  np.zeros(bearing_size,dtype=float)
    bearing_freq_values = []
    fault_index = 0
    bearing_freq_possibility = 0
    for ii in range(bearing_size):
        _f_BPFI,max_fvalues_main1X, max_fvalues_main2X, max_fvalues_main3X, max_fvalues_main4X = get_multi_freq_amplitide(fs, f_BPFI[ii],real_amp, freq_x, N, deltf) 
        bearing_BPFI_enery_temp =  (max_fvalues_main1X /2 *N)**2/N/fs*2 + (max_fvalues_main2X /2 *N)**2/N/fs*2 + (max_fvalues_main3X /2 *N)**2/N/fs*2 + (max_fvalues_main4X /2 *N)**2/N/fs*2
        if BPFI_set_lines !=-1 and BPFI_set_lines > 4:
            for bpfi_i in range(5,BPFI_set_lines+1):
                BPFI_iX = _f_BPFI * bpfi_i
                if BPFI_iX > fs/2.56:
                    # print(f'BPFI_iX频率设置溢出采集范围:break loose_i!')
                    break
                max_fvalues_BPFIiX = get_max_fvalues_speednX_value(BPFI_iX,deltf,N,fs,real_amp,freq_x)
                BPFI_enery_i_temp = (max_fvalues_BPFIiX /2 *N)**2/N/fs*2
                bearing_BPFI_enery_temp += BPFI_enery_i_temp
        bearing_BPFI_enery_array[ii] = bearing_BPFI_enery_temp
        bearing_freq_values.append([max_fvalues_main1X,max_fvalues_main2X,max_fvalues_main3X,max_fvalues_main4X]) #
    
    bearing_BPFI_enery_ratio = np.sum(bearing_BPFI_enery_array) / time_power
    # print(f'gear per ratio= {bearing_BPFI_enery_ratio} ') #
    if model_bearing_BPFI_para != -1:
        res = 0
        while res < len(model_bearing_BPFI_para) and bearing_BPFI_enery_ratio >= model_bearing_BPFI_para[res] :
            res += 1
        print(bearing_BPFI_enery_ratio, res,model_bearing_BPFI_para)
        bearing_BPFI_level = res
        if res == 0:
            bearing_BPFI = 0
            # fault_index.append(0)
            fault_index = 0
        else:
            # fault_index.append(np.argmax(bearing_BPFI_enery_array) + 1)
            fault_index = np.argmax(bearing_BPFI_enery_array) + 1
            bearing_BPFI = 1
            bearing_freq_possibility = 0.6 + 0.4/(1 - model_bearing_BPFI_para[0])*(bearing_BPFI_enery_ratio-model_bearing_BPFI_para[0])
            # print(f' BPFI, bearing_freq_possibility={bearing_freq_possibility}')
    else:
        bearing_BPFI = 0 
        bearing_BPFI_level = 0
    # print(f'bearing_BPFI={bearing_BPFI},bearing_BPFI_level={bearing_BPFI_level},fault_index={fault_index},bearing_freq_possibility={bearing_freq_possibility}')
    return bearing_BPFI,bearing_BPFI_level,fault_index,bearing_freq_possibility,bearing_freq_values,bearing_BPFI_enery_ratio



