'''
振动筛-激振器-轴承故障,松动故障
threshold_middle_f4，threshold_high_f4
'''
import os
import sys
import numpy as np
import pandas as pd
import ast
CURRENT_PATH = os.path.dirname(os.path.realpath(__file__))
sys.path.append(os.path.join(CURRENT_PATH, '..'))
from utils.scipy_fft import fft_plot,envelope_plot
from scipy.signal import decimate
from utils.common_use import get_section_freq_max
from utils.get_freq_power_per import get_freq_power_per
from utils.common_use import get_section_freq_max,get_max_fvalues_speednX_value #
from mechanism.mm_parent import MM_PARENT
from log.logger import LOG



# 振动筛轴承故障
class SIFTERBEARING(MM_PARENT):
    def __init__(self, args):
        super(SIFTERBEARING, self).__init__(args)

    # 机理模型诊断
    def bearing_diagnosis(self):
        # data1 = wave_array
        data1 = self.args.wave_array
        fs = self.args.fs
        N = len(data1)
        model_bearing_para = self.args.model_fault_ratio 
        if model_bearing_para != -1:
            model_bearing_para = np.linspace(model_bearing_para,1,5).tolist()[:-1]  
        
        threshold_middle_f4 = ast.literal_eval(self.args.model_fault_set_para).get('middle_freq_weight',-1)#0.05 10* 
        threshold_high_f4 = ast.literal_eval(self.args.model_fault_set_para).get('high_freq_weight',-1)# 0.6 1*
        LOG.info(f'threshold_middle_f4={threshold_middle_f4},threshold_high_f4={threshold_high_f4}')
        time_power = np.sum(np.power(data1, 2))/fs
        bearing_freq_possibility = 0
        if fs <10000:
            low_freq = 200
            middle_freq = 1000
            max_freq = fs/2.56
            low_freq_per,middle_freq_per,high_freq_per = get_freq_power_per(data1,fs,low_freq,middle_freq,max_freq)
            print(f'low_freq_per={low_freq_per},middle_freq_per={middle_freq_per},high_freq_per={high_freq_per}')
            # bearing_freq_values = [middle_freq_per,high_freq_per]
            bearing_freq_values = {'中频特征':middle_freq_per,'高频特征':high_freq_per}
            bearing_enery_ratio = threshold_middle_f4*middle_freq_per + threshold_high_f4*high_freq_per
            LOG.info(f'bearing_enery_ratio = {bearing_enery_ratio},model_bearing_para={model_bearing_para}')
            if model_bearing_para !=-1 :#
                res = 0
                while res < len(model_bearing_para) and bearing_enery_ratio >= model_bearing_para[res] :
                    res += 1
                bearing_structure_level = res
            else:
                bearing_structure_level = 0

        else:
            bearing_structure_level = 0#fs>10000
            bearing_enery_ratio = 0
        # bearing_enery_ratio = (max_fvalues_speed1X /2 *N)**2/N/fs*2
        if bearing_structure_level:
            bearing_freq_possibility = 0.6 + 0.4/(1-model_bearing_para[0])*(bearing_enery_ratio-model_bearing_para[0])
        else:
            pass


        return bearing_structure_level,bearing_enery_ratio,bearing_freq_values,bearing_freq_possibility
    
    # 机理模型诊断
    def loose_diagnosis(self, wave_array):
        x = wave_array
        fs = self.args.fs
        speed = self.args.speed
        model_loose_para = self.args.model_fault_ratio 
        if model_loose_para != -1: # 反向
            model_loose_para = np.linspace(model_loose_para,1,5).tolist()[:-1]  
        model_loose_para = [1-i for i in model_loose_para]
        LOG.info(f'reverse-> model_loose_para={model_loose_para}')

        # if model_loose_para != -1:
        #     model_loose_para = np.linspace(model_loose_para,1,5).tolist()[:-1]  
        
        loose_set_lines = ast.literal_eval(self.args.model_fault_set_para).get('loose_structure_lines',-1)
        time_power = np.sum(np.power(x, 2))/fs
        # print(f'time_power={time_power}')
        loose_structure = 0
        loose_structure_level = 0
        speed_freq_possibility = 0
        N = len(x)
        deltf = fs / N
        speed_freq = speed/60
        speed_freq_left = speed_freq - 3
        speed_point_left = int(speed_freq_left * N / fs)
        speed_freq_right = speed_freq + 3
        speed_point_right = int(speed_freq_right * N / fs + 0.5)
        freq_x,real_amp,_angle = fft_plot(x, fs)
        if speed_point_left < 0:
            # print(f'error set left point,speed_point_left = {speed_point_left}, set to zero!')
            speed_point_left = 0
        if speed_point_left > len(real_amp) or speed_point_right > len(real_amp):
            LOG.info('!!!!!!转速设置超出采集频率范围:不诊断!!!!!!')
            return 0,0,0,0,0,0,[],[]
        if speed_point_left == 0 and speed_point_right == 0:
            return 0,0,0,0,0,0,[],[]
        max_value_loc = np.argmax(real_amp[speed_point_left:speed_point_right])
        # print(real_amp[speed_point_left:speed_point_right])
        judge_array = np.delete(real_amp[speed_point_left:speed_point_right],max_value_loc)
        up_line = np.mean(judge_array) + 3*np.std(judge_array)
        max_value_loc += speed_point_left
        max_fvalues = real_amp[max_value_loc]
        if max_fvalues > up_line:
            speed_1X = freq_x[max_value_loc]
            # print(f'修正转频:to max_f={speed_1X}')
        else:
            # print('使用额定转频')
            speed_1X = speed_freq
        max_fvalues_speed1X = max_fvalues.copy()
        # print(f'max_fvalues_speed1X={max_fvalues_speed1X}')
        unbalance_enery = (max_fvalues_speed1X /2 *N)**2/N/fs*2
        # print(f'unbalance_enery:{unbalance_enery}')
        unbalance_enery_ratio = unbalance_enery/time_power
        # LOG.info(f'unbalance_enery_ratio={unbalance_enery_ratio}')
        speed_2X = speed_1X * 2
        speed_3X = speed_1X * 3
        speed_4X = speed_1X * 4
        max_fvalues_speed2X = get_max_fvalues_speednX_value(speed_2X,deltf,N,fs,real_amp,freq_x)
        max_fvalues_speed3X = get_max_fvalues_speednX_value(speed_3X,deltf,N,fs,real_amp,freq_x)
        max_fvalues_speed4X = get_max_fvalues_speednX_value(speed_4X,deltf,N,fs,real_amp,freq_x)
        # misalignment_enery = (max_fvalues_speed2X /2 *N)**2/N/fs*2 + unbalance_enery
        misalignment_enery = (max_fvalues_speed2X /2 *N)**2/N/fs*2 + unbalance_enery * 0.5
        misalignment_enery_ratio = misalignment_enery/time_power
        # LOG.info(f'misalignment_enery_ratio={misalignment_enery_ratio}')

        loose_enery = (max_fvalues_speed4X /2 *N)**2/N/fs*2 + (max_fvalues_speed3X /2 *N)**2/N/fs*2 + misalignment_enery
        if loose_set_lines !=-1 and loose_set_lines > 4:
            for loose_i in range(5,loose_set_lines+1):
                # print(f'loose_i={loose_i}')
                speed_iX = speed_1X * loose_i
                if speed_iX > fs/2.56:
                    LOG.info(f'频率设置溢出采集范围:break loose_i!')
                    break
                max_fvalues_speediX = get_max_fvalues_speednX_value(speed_iX,deltf,N,fs,real_amp,freq_x)
                loose_enery_i_temp = (max_fvalues_speediX /2 *N)**2/N/fs*2
                loose_enery += loose_enery_i_temp

        loose_enery_ratio = loose_enery / time_power
        LOG.info(f'loose_enery_ratio={loose_enery_ratio}')
        # print(f'loose_enery={loose_enery},loose_enery_ratio={loose_enery_ratio}')

        if model_loose_para !=-1 :#
            res = 0
            while res < len(model_loose_para) and loose_enery_ratio >= model_loose_para[res] :
                res += 1
            # print(loose_enery_ratio, res,model_loose_para)
            loose_structure_level = res
            if res == 0:
                loose_structure = 0
            else:
                loose_structure = 1
        else:
            loose_structure = 0 
            loose_structure_level = 0
        # speed_freq_values = [max_fvalues_speed1X,max_fvalues_speed2X,max_fvalues_speed3X,max_fvalues_speed4X]
        speed_freq_values = {"转频1X":max_fvalues_speed1X,"转频2X":max_fvalues_speed2X,"转频3X":max_fvalues_speed3X,"转频4X":max_fvalues_speed4X}
        if loose_structure:
            speed_freq_possibility = 0.6 + 0.4/(1-model_loose_para[0])*(loose_enery_ratio-model_loose_para[0])
        # print(f'unbalance_enery_ratio={unbalance_enery_ratio},misalignment_enery_ratio={misalignment_enery_ratio},loose_enery_ratio')
        # print(f'speed_freq_possibility={speed_freq_possibility}')
        
        return loose_structure_level,loose_enery_ratio,speed_freq_values,speed_freq_possibility