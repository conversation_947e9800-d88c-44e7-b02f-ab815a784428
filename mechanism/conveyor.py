'''
conveyor: 皮带机
'''
import numpy as np
from scipy import signal
import os
import ast
import sys
CURRENT_PATH = os.path.dirname(os.path.realpath(__file__))
sys.path.append(os.path.join(CURRENT_PATH, '..'))
import matplotlib
matplotlib.use('TkAgg')
import matplotlib.pyplot as plt
plt.rcParams['font.sans-serif']=['SimHei'] #用来正常显示中文标签
plt.rcParams['axes.unicode_minus']=False #用来正常显示负号
from log.logger import LOG
from utils.scipy_fft import fft_plot,envelope_plot


class BeltFaultSimulator:
    def __init__(self, sampling_rate=5120, duration=1.0):
        """
        初始化皮带故障模拟器
        sampling_rate: 采样率
        duration: 信号持续时间(秒)
        """
        self.sampling_rate = sampling_rate
        self.duration = duration
        self.time = np.linspace(0, duration, int(sampling_rate * duration))
        
    def generate_belt_resonance(self, belt_freq=10, resonance_freq=1000, noise_level=0.1):
        """模拟皮带共振故障"""
        # 基础皮带运转信号
        base_signal = np.sin(2 * np.pi * belt_freq * self.time)
        
        # 共振信号（调制）
        resonance = np.sin(2 * np.pi * resonance_freq * self.time)
        modulated = base_signal * (1 + resonance)
        
        # 添加噪声
        noise = np.random.normal(0, noise_level, len(self.time))
        return modulated + noise
        
    def generate_belt_wear(self, belt_freq=10, wear_impact_freq=30, severity=0.5):
        """模拟皮带磨损故障"""
        # 基础信号
        base_signal = np.sin(2 * np.pi * belt_freq * self.time)
        
        # 磨损冲击
        wear_impacts = np.zeros_like(self.time)
        impact_interval = int(self.sampling_rate / wear_impact_freq)
        wear_impacts[::impact_interval] = severity
        
        # 使用指数衰减模拟冲击响应
        decay = np.exp(-5 * np.linspace(0, 1, impact_interval))
        wear_response = np.convolve(wear_impacts, decay, mode='same')
        
        return base_signal + wear_response
        
    def generate_belt_looseness(self, belt_freq=10, looseness_factor=0.3):
        """模拟皮带松动故障"""
        # 基础信号
        base_signal = np.sin(2 * np.pi * belt_freq * self.time)
        
        # 添加2X和3X分量模拟松动
        harmonic_2x = looseness_factor * np.sin(4 * np.pi * belt_freq * self.time)
        harmonic_3x = (looseness_factor/2) * np.sin(6 * np.pi * belt_freq * self.time)
        
        # 添加随机波动
        random_fluctuation = looseness_factor * np.random.normal(0, 0.1, len(self.time))
        
        return base_signal + harmonic_2x + harmonic_3x + random_fluctuation
        
    def generate_belt_misalignment(self, belt_freq=10, misalignment_severity=0.4):
        """模拟皮带不对中故障"""
        # 基础信号
        base_signal = np.sin(2 * np.pi * belt_freq * self.time)
        
        # 不对中特征：2X分量显著
        misalignment_2x = misalignment_severity * np.sin(4 * np.pi * belt_freq * self.time)
        
        # 轴向振动分量（使用更高频率模拟）
        axial_vibration = (misalignment_severity/2) * np.sin(8 * np.pi * belt_freq * self.time)
        
        return base_signal + misalignment_2x + axial_vibration
        
    def generate_belt_eccentricity(self, belt_freq=10, eccentricity_level=0.3):
        """模拟皮带偏心故障"""
        # 基础信号（1X显著）
        base_signal = (1 + eccentricity_level) * np.sin(2 * np.pi * belt_freq * self.time)
        
        # 添加幅值调制
        modulation = 1 + eccentricity_level * np.sin(2 * np.pi * belt_freq * self.time)
        modulated_signal = base_signal * modulation
        
        return modulated_signal

class BeltFaultDiagnostics:
    def __init__(self,args):
        super(BeltFaultDiagnostics, self).__init__(args)
        """初始化诊断器"""
        # self.fault_thresholds = {
        #     'resonance': {'amplitude_ratio': 0.3, 'high_freq_energy': 0.2},
        #     'wear': {'impact_energy': 0.25, 'kurtosis': 4.0},
        #     'looseness': {'harmonic_ratio': 0.2, 'random_energy': 0.15},
        #     'misalignment': {'2x_ratio': 0.3, 'axial_energy': 0.25},
        #     'eccentricity': {'1x_amplitude': 0.4, 'modulation_index': 0.2}
        # }
    
    def calculate_spectrum(self, data, sampling_rate):
        """计算频谱"""
        # spectrum = np.fft.fft(data)
        # freq = np.fft.fftfreq(len(data), 1/sampling_rate)
        # 计算频谱
        freq_x,real_amp,_angle = fft_plot(data, sampling_rate)
        return freq_x, real_amp
    
    def calculate_envelope_spectrum(self, data, sampling_rate):
        """计算包络谱"""
        analytic_signal = signal.hilbert(data)
        envelope = np.abs(analytic_signal)
        envelope_spectrum = np.fft.fft(envelope)
        freq = np.fft.fftfreq(len(data), 1/sampling_rate)
        return freq[:len(freq)//2], np.abs(envelope_spectrum)[:len(envelope_spectrum)//2]
    
    
    def diagnose_resonance(self, data, sampling_rate):
        """诊断共振故障"""
        # model_para = args.model_fault_ratio 
        
        amplitude_ratio1 = self.args.model_fault_ratio #
        if amplitude_ratio1 != -1:
            amplitude_ratio1 = np.linspace(amplitude_ratio1,1,5).tolist()[:-1] 

        high_freq_energy1 = ast.literal_eval(self.args.model_fault_set_para).get('high_freq_energy',-1)#

        base_band_lowfreq = ast.literal_eval(self.args.model_fault_set_para).get('base_band_lowfreq',-1)#
        resonance_band_left = ast.literal_eval(self.args.model_fault_set_para).get('resonance_band_left',-1)#
        resonance_band_right = ast.literal_eval(self.args.model_fault_set_para).get('resonance_band_right',-1)#
        high_freq = ast.literal_eval(self.args.model_fault_set_para).get('high_freq',-1)#

        LOG.info(f'in CONVEYOR model_fault_ratio = {amplitude_ratio1},high_freq_energy={high_freq_energy},base_band_lowfreq={base_band_lowfreq},resonance_band_left={resonance_band_left},resonance_band_right={resonance_band_right},high_freq={high_freq}')



        freq, spectrum = self.calculate_spectrum(data, sampling_rate)
        
        # 计算高频能量比
        high_freq_idx = freq > high_freq
        high_freq_energy = np.sum(spectrum[high_freq_idx]) / np.sum(spectrum)
        
        # 计算共振幅值比
        resonance_band = (resonance_band_left < freq) & (freq < resonance_band_right)
        base_band = (0 < freq) & (freq < base_band_lowfreq)
        amplitude_ratio = np.max(spectrum[resonance_band]) / np.max(spectrum[base_band])
        LOG.info(f'amplitude_ratio={amplitude_ratio},high_freq_energy={high_freq_energy}')
        #计算
        resonance_values = {'共振比例系数':amplitude_ratio,'高频特征比例':high_freq_energy}
        resonance_possibility = 0
        if amplitude_ratio1 !=-1 :#
            res = 0
            while res < len(amplitude_ratio1) and amplitude_ratio >= amplitude_ratio1[res] :
                res += 1
            resonance_level = res
        else:
            resonance_level = 0
        if resonance_level:
            resonance_possibility = 0.6 + 0.4/(1-amplitude_ratio1[0])*(amplitude_ratio-amplitude_ratio1[0])
        return resonance_level,amplitude_ratio,resonance_values,resonance_possibility

    
    def diagnose_wear(self, data, sampling_rate):
        """诊断皮带磨损"""
        freq, envelope_spectrum = self.calculate_envelope_spectrum(data, sampling_rate)
        
        # 计算冲击能量
        impact_energy = np.sum(envelope_spectrum) / len(envelope_spectrum)
        
        # 计算峭度
        kurtosis = np.mean((data - np.mean(data))**4) / (np.std(data)**4)
        
        is_wear = (impact_energy > self.fault_thresholds['wear']['impact_energy'] and 
                  kurtosis > self.fault_thresholds['wear']['kurtosis'])
        
        return {
            'is_wear': is_wear,
            'impact_energy': impact_energy,
            'kurtosis': kurtosis
        }
    
    def diagnose_looseness(self, data, sampling_rate):
        """诊断皮带松动"""
        freq, spectrum = self.calculate_spectrum(data, sampling_rate)
        
        # 计算谐波比
        fundamental_idx = np.argmax(spectrum[:len(spectrum)//4])
        fundamental_freq = freq[fundamental_idx]
        fundamental_amp = spectrum[fundamental_idx]
        
        harmonic_2x_idx = np.argmin(np.abs(freq - 2*fundamental_freq))
        harmonic_3x_idx = np.argmin(np.abs(freq - 3*fundamental_freq))
        
        harmonic_ratio = (spectrum[harmonic_2x_idx] + spectrum[harmonic_3x_idx]) / fundamental_amp
        
        # 计算随机能量
        random_energy = np.std(data) / np.mean(np.abs(data))
        
        is_loose = (harmonic_ratio > self.fault_thresholds['looseness']['harmonic_ratio'] and 
                   random_energy > self.fault_thresholds['looseness']['random_energy'])
        
        return {
            'is_loose': is_loose,
            'harmonic_ratio': harmonic_ratio,
            'random_energy': random_energy
        }
    
    def diagnose_misalignment(self, data, sampling_rate):
        """诊断皮带不对中"""
        freq, spectrum = self.calculate_spectrum(data, sampling_rate)
        
        # 计算2X分量比
        fundamental_idx = np.argmax(spectrum[:len(spectrum)//4])
        fundamental_freq = freq[fundamental_idx]
        fundamental_amp = spectrum[fundamental_idx]
        
        harmonic_2x_idx = np.argmin(np.abs(freq - 2*fundamental_freq))
        ratio_2x = spectrum[harmonic_2x_idx] / fundamental_amp
        
        # 计算高频轴向能量
        axial_band = (freq > 3*fundamental_freq) & (freq < 5*fundamental_freq)
        axial_energy = np.sum(spectrum[axial_band]) / np.sum(spectrum)
        
        is_misaligned = (ratio_2x > self.fault_thresholds['misalignment']['2x_ratio'] and 
                        axial_energy > self.fault_thresholds['misalignment']['axial_energy'])
        
        return {
            'is_misaligned': is_misaligned,
            '2x_ratio': ratio_2x,
            'axial_energy': axial_energy
        }
    
    def diagnose_eccentricity(self, data, sampling_rate):
        """诊断皮带偏心"""
        freq, spectrum = self.calculate_spectrum(data, sampling_rate)
        
        # 计算1X分量幅值
        fundamental_idx = np.argmax(spectrum[:len(spectrum)//4])
        fundamental_amp = spectrum[fundamental_idx]
        
        # 计算调制指数
        envelope_freq, envelope_spectrum = self.calculate_envelope_spectrum(data, sampling_rate)
        modulation_index = np.max(envelope_spectrum[1:]) / envelope_spectrum[0]
        
        is_eccentric = (fundamental_amp > self.fault_thresholds['eccentricity']['1x_amplitude'] and 
                       modulation_index > self.fault_thresholds['eccentricity']['modulation_index'])
        
        return {
            'is_eccentric': is_eccentric,
            '1x_amplitude': fundamental_amp,
            'modulation_index': modulation_index
        }


# 皮带机共振故障诊断
# 'amplitude_ratio': 0.3, 'high_freq_energy': 0.2， base_band_lowfreq:100,resonance_band_left:900,resonance_band_right:1100,high_freq:500
class CONVEYOR:
    def __init__(self):
        self.fault_types = {
            "RESONANCE": "共振"
        }
    
    @classmethod
    def diagnosis(cls, args):
        fs = args.fs
        vibration_data = args.wave_array

        result = []
        
        # 创建诊断实例
        vfd = BeltFaultDiagnostics(args) #SIFTERBEARING -- bearing_fuzzy --
        LOG.info(f'args.model_type={args.model_type}') 
        # 执行各类故障检测
        if args.model_type == 'RESONANCE':
            result = vfd.diagnose_resonance(vibration_data, fs)

        LOG.info(f'result={result}')
        return result


def plot_diagnosis_results(time, data, sampling_rate, title):
    """绘制诊断结果"""
    plt.figure(figsize=(12, 8))
    
    # 时域波形
    plt.subplot(311)
    plt.plot(time, data)
    plt.title(f'{title} - 时域波形')
    plt.xlabel('时间 (s)')
    plt.ylabel('幅值')
    plt.grid(True)
    
    # 频谱
    # freq = np.fft.fftfreq(len(data), 1/sampling_rate)
    # spectrum = np.fft.fft(data)
    # fft
    freq,spectrum,_angle = fft_plot(data, sampling_rate)

    plt.subplot(312)
    plt.plot(freq, spectrum)
    plt.title('频谱')
    plt.xlabel('频率 (Hz)')
    plt.ylabel('幅值')
    plt.grid(True)
    
    # 包络谱
    # analytic_signal = signal.hilbert(data)
    # envelope = np.abs(analytic_signal)
    # envelope_spectrum = np.fft.fft(envelope)
    #envelope
    freq,envelope_spectrum = envelope_plot(data,sampling_rate)

    plt.subplot(313)
    plt.plot(freq, envelope_spectrum)
    plt.title('包络谱')
    plt.xlabel('频率 (Hz)')
    plt.ylabel('幅值')
    plt.grid(True)
    
    plt.tight_layout()
    plt.show()


# 使用示例
if __name__ == '__main__':
    pass
    # # 创建模拟器和诊断器
    # simulator = BeltFaultSimulator(sampling_rate=5120, duration=1.0)
    # diagnostics = BeltFaultDiagnostics()
    
    # # 生成各种故障数据
    # fault_data = {
    #     '皮带共振': simulator.generate_belt_resonance(),
    #     '皮带磨损': simulator.generate_belt_wear(),
    #     '皮带松动': simulator.generate_belt_looseness(),
    #     '皮带不对中': simulator.generate_belt_misalignment(),
    #     '皮带偏心': simulator.generate_belt_eccentricity()
    # }
    
    # # 诊断并显示结果
    # for fault_name, data in fault_data.items():
    #     print(f"\n=== {fault_name}故障诊断结果 ===")
        
    #     # 进行诊断
    #     resonance_result = diagnostics.diagnose_resonance(data, simulator.sampling_rate)
    #     wear_result = diagnostics.diagnose_wear(data, simulator.sampling_rate)
    #     looseness_result = diagnostics.diagnose_looseness(data, simulator.sampling_rate)
    #     misalignment_result = diagnostics.diagnose_misalignment(data, simulator.sampling_rate)
    #     eccentricity_result = diagnostics.diagnose_eccentricity(data, simulator.sampling_rate)
        
    #     # 显示诊断结果
    #     print(f"共振故障: {'是' if resonance_result['is_resonance'] else '否'}")
    #     print(f"磨损故障: {'是' if wear_result['is_wear'] else '否'}")
    #     print(f"松动故障: {'是' if looseness_result['is_loose'] else '否'}")
    #     print(f"不对中故障: {'是' if misalignment_result['is_misaligned'] else '否'}")
    #     print(f"偏心故障: {'是' if eccentricity_result['is_eccentric'] else '否'}")
        
    #     # 绘制诊断图表
    #     plot_diagnosis_results(simulator.time, data, simulator.sampling_rate, fault_name)

