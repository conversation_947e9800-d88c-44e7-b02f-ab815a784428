'''
diagnose_rotor_eccentricity
date:20250630,slip
'''
import numpy as np
import os
import sys
CURRENT_PATH = os.path.dirname(os.path.realpath(__file__))
sys.path.append(os.path.join(CURRENT_PATH, '..'))
from utils.scipy_fft import fft_plot
from utils.common_use import get_section_freq_max,get_max_fvalues_speednX_value # 
from log.logger import LOG


from mechanism.mm_parent import MM_PARENT


class BEARINGSLIP(MM_PARENT):
    def __init__(self, args):
        super(BEARINGSLIP, self).__init__(args)

    def _detect_harmonics(self, freqs, psd, base_freq, max_harmonic=5):
        """检测基频的谐波成分"""
        harmonic_amplitudes = []
        for i in range(1, max_harmonic+1):
            freq = base_freq * i
            idx = np.argmin(np.abs(freqs - freq))
            harmonic_amplitudes.append(psd[idx])
        return np.array(harmonic_amplitudes)
    
    # 机理模型诊断
    def diagnosis(self, wave_array):
        x = wave_array
        bearing_parameter = self.args.bearing_parameter
        # LOG.info(f'wave_array={wave_array}')
        fs = self.args.fs
        speed = self.args.speed
        LOG.info(f'fs={fs},speed={speed}')
        # model_unbalance_para = self.args.get('unbalance',-1)
        model_slip_para = self.args.model_fault_ratio 
        bearing_parameter = [float(i) for i in bearing_parameter]
        LOG.info(f'model_slip_para={model_slip_para}')
        if model_slip_para != -1:
            model_slip_para = np.linspace(model_slip_para,1,5).tolist()[:-1]  
        time_power = np.sum(np.power(x, 2))/fs
        # print(f'time_power={time_power}')
        slip = 0
        slip_level = 0
        speed_freq_possibility = 0
        N = len(x)
        deltf = fs / N
        speed_freq = speed/60
        freqs,psd,_angle = fft_plot(x, fs)

        if bearing_parameter is  None or len(bearing_parameter) == 0 :
            return 0,0,[],0
        bpfi = bearing_parameter[0]
        bpfo = bearing_parameter[1]
        # 检测内圈跑圈特征频率 (BPFI ± n×轴频)
        inner_slippage_freqs = [bpfi + i*speed_freq for i in range(-2, 3)]
        inner_amps = []
        for freq in inner_slippage_freqs:
            idx = np.argmin(np.abs(freqs - freq))
            inner_amps.append(psd[idx])
        
        # 检测外圈跑圈特征频率 (BPFO ± n×轴频)
        outer_slippage_freqs = [bpfo + i*speed_freq for i in range(-2, 3)]
        outer_amps = []
        for freq in outer_slippage_freqs:
            idx = np.argmin(np.abs(freqs - freq))
            outer_amps.append(psd[idx])
        
        # 判断准则：组合频率幅值超过平均幅值3倍
        mean_amp = np.mean(psd)
        inner_slippage = np.max(inner_amps) > 3 * mean_amp
        outer_slippage = np.max(outer_amps) > 3 * mean_amp

        slip_enery = (np.mean(inner_amps) /2 *N)**2/N/fs*2 * 5 + (np.mean(outer_amps) /2 *N)**2/N/fs*2 * 5
        print(f'slip_enery:{slip_enery}')
        slip_enery_ratio = slip_enery/time_power
        LOG.info(f'slip_enery_ratio={slip_enery_ratio}')
        if model_slip_para != -1 and (inner_slippage or outer_slippage):
            res = 0
            while res < len(model_slip_para) and slip_enery_ratio >= model_slip_para[res]:
                res += 1
            slip_level = res
            LOG.info(slip_enery_ratio, res,model_slip_para)
            if res == 0:
                slip = 0
            else:
                slip = 1
        else:
            slip = 0
            slip_level = 0
        speed_freq_values = {'BPFI':np.max(inner_amps),'BPFO':np.max(outer_amps)}
        if slip:
            speed_freq_possibility = 0.6 + 0.4/(1 - model_slip_para[0])*(slip_enery_ratio-model_slip_para[0]) #
        
        return slip_level,slip_enery_ratio,speed_freq_values,speed_freq_possibility
