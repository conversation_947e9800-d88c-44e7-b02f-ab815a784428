'''
unbalance
misalignment,loose
'''
import numpy as np
import os
import sys
CURRENT_PATH = os.path.dirname(os.path.realpath(__file__))
sys.path.append(os.path.join(CURRENT_PATH, '..'))
from utils.scipy_fft import fft_plot
from utils.common_use import get_section_freq_max,get_max_fvalues_speednX_value # 返回3
from log.logger import LOG


from mechanism.mm_parent import MM_PARENT


class MISALIGNMENT(MM_PARENT):
    def __init__(self, args):
        super(MISALIGNMENT, self).__init__(args)

    # 机理模型诊断
    def diagnosis(self, wave_array):
        x = wave_array
        fs = self.args.fs
        speed = self.args.speed
        # model_misalignment_para = self.args.get('misalignment',-1)
        model_misalignment_para = self.args.model_fault_ratio 
        if model_misalignment_para != -1:
            model_misalignment_para = np.linspace(model_misalignment_para,1,5).tolist()[:-1]  
        time_power = np.sum(np.power(x, 2))/fs
        # print(f'time_power={time_power}')
        misalignment = 0
        misalignment_level = 0
        speed_freq_possibility = 0
        N = len(x)
        deltf = fs / N
        speed_freq = speed/60
        speed_freq_left = speed_freq - 3
        speed_point_left = int(speed_freq_left * N / fs)
        speed_freq_right = speed_freq + 3
        speed_point_right = int(speed_freq_right * N / fs + 0.5)
        freq_x,real_amp,_angle = fft_plot(x, fs)
        if speed_point_left < 0:
            # print(f'error set left point,speed_point_left = {speed_point_left}, set to zero!')
            speed_point_left = 0
        if speed_point_left > len(real_amp) or speed_point_right > len(real_amp):
            LOG.info('!!!!!!转速设置超出采集频率范围:不诊断!!!!!!')
            return 0,0,[],0
        if speed_point_left == 0 and speed_point_right == 0:
            return 0,0,[],0
        max_value_loc = np.argmax(real_amp[speed_point_left:speed_point_right])
        # print(real_amp[speed_point_left:speed_point_right])
        judge_array = np.delete(real_amp[speed_point_left:speed_point_right],max_value_loc)
        up_line = np.mean(judge_array) + 3*np.std(judge_array)
        max_value_loc += speed_point_left
        max_fvalues = real_amp[max_value_loc]
        if max_fvalues > up_line:
            speed_1X = freq_x[max_value_loc]
            # print(f'修正转频:to max_f={speed_1X}')
        else:
            # print('使用额定转频')
            speed_1X = speed_freq
        max_fvalues_speed1X = max_fvalues.copy()
        # print(f'max_fvalues_speed1X={max_fvalues_speed1X}')
        unbalance_enery = (max_fvalues_speed1X /2 *N)**2/N/fs*2
        # print(f'unbalance_enery:{unbalance_enery}')
        unbalance_enery_ratio = unbalance_enery/time_power
        # LOG.info(f'unbalance_enery_ratio={unbalance_enery_ratio}')

        speed_2X = speed_1X * 2

        max_fvalues_speed2X = get_max_fvalues_speednX_value(speed_2X,deltf,N,fs,real_amp,freq_x)

        # misalignment_enery = (max_fvalues_speed2X /2 *N)**2/N/fs*2 + unbalance_enery
        misalignment_enery = (max_fvalues_speed2X /2 *N)**2/N/fs*2 + unbalance_enery * 0.5
        misalignment_enery_ratio = misalignment_enery/time_power
        LOG.info(f'misalignment_enery_ratio={misalignment_enery_ratio}')
        if model_misalignment_para != -1 :#and misalignment2X != -1
            res = 0
            while res < len(model_misalignment_para) and misalignment_enery_ratio >= model_misalignment_para[res]:#and max_fvalues_speed2X > misalignment2X[res]
                res += 1
            print(misalignment_enery_ratio, res,model_misalignment_para)
            misalignment_level = res
            if res == 0:
                misalignment = 0
            else:
                misalignment = 1
        else:
            misalignment = 0
            misalignment_level = 0
        # print(f'loose_enery={loose_enery},loose_enery_ratio={loose_enery_ratio}')


        # speed_freq_values = [max_fvalues_speed1X,max_fvalues_speed2X]
        speed_freq_values = {"转频1X":max_fvalues_speed1X,"转频2X":max_fvalues_speed2X}

        if misalignment:
            speed_freq_possibility = 0.6 + 0.4/(1-model_misalignment_para[0])*(misalignment_enery_ratio-model_misalignment_para[0])

        # print(f'speed_freq_possibility={speed_freq_possibility}')
        
        return misalignment_level, misalignment_enery_ratio,speed_freq_values,speed_freq_possibility
