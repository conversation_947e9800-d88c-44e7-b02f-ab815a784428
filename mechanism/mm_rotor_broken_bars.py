'''
电流故障诊断测试:20250214
rotor_broken_bars 转子断条 ROTORBROKENBARS
rotor_broken_lines:2
'''
import numpy as np
import os
import sys
import ast
import pywt
from scipy import signal
from scipy.fft import fft
CURRENT_PATH = os.path.dirname(os.path.realpath(__file__))
sys.path.append(os.path.join(CURRENT_PATH, '..'))
import warnings
warnings.filterwarnings('ignore')   
from log.logger import LOG
from mechanism.mm_parent import MM_PARENT


class ROTORBROKENBARS(MM_PARENT):
    def __init__(self, args):   #
        super(ROTORBROKENBARS, self).__init__(args)
        self.fs = self.args.fs  # 采样频率sampling_rate
        self.power_freq = 50  # 电源频率(Hz)
        self.p = self.args.p # 级数 
        self.rotor_speed = self.args.speed
        self.rotor_slots = self.args.rotor_slots # 转子槽数
        self.slip = self.args.slip #转差率

    def diagnosis(self,current):
        """转子断条故障诊断"""
        rotor_broken_lines = 2
        rotor_broken_bars_possibility = 0
        freq, spectrum = self.fft_analysis(self.current)
        time_power = np.sum(np.power(current, 2))/self.fs
        N = len(current)
        fr = self.rotor_speed / 60
        deltf = self.fs/N
        model_para = self.args.model_fault_ratio 
        fixed_array = [48,42,36,30,0,float('-inf')]
        if model_para != -1:
            model_para = model_para * fixed_array #np.linspace(model_para,1,5).tolist()[:-1]  
        # loose_set_lines = ast.literal_eval(self.args.model_fault_set_para).get('loose_structure_lines',-1)
        LOG.info(f'model_para={model_para}')
        # 特征频率 fs(1±2ks)
        # k_values = [1, 2]  # 考虑k=1,2的情况
        k_values = [i+1 for i in range(rotor_broken_lines)]  # 考虑k=1,2的情况

        # k_values = [1]  # 考虑k=1,2的情况
        feature_freqs = [self.power_freq]
        for k in k_values:
            print(f'k={k},  {self.power_freq - self.p*k*self.slip} VS {self.power_freq + self.p*k*self.slip}')
            feature_freqs.extend([
                self.power_freq - self.p*k*self.slip,
                self.power_freq + self.p*k*self.slip
            ])
        
        fault_indicators = []
        for target_freq in feature_freqs:
            if target_freq < self.fs/2.56 and target_freq > 0:
                mask = (freq >= target_freq - 3*self.deltf) & (freq <= target_freq + 3*self.deltf)
                print(f'target_freq={target_freq},freq[mask]={freq[mask]}')
                peak_amp = np.max(spectrum[mask])
                fault_indicators.append(peak_amp)
        print(f'fault_indicators={fault_indicators}')
        print(f'self.time_power={self.time_power}')
        rotor_broken_bars_enery = 0 
        for broken_i in fault_indicators[1:]:
            print(f'broken_i={broken_i}')
            rotor_broken_bars_enery += (broken_i /2 *self.N)**2/self.N/self.fs*2
        print(f'rotor_broken_bars_enery={rotor_broken_bars_enery}')
        rotor_broken_bars_enery_ratio = rotor_broken_bars_enery/time_power
        LOG.info(f'rotor_broken_bars_enery_ratio={rotor_broken_bars_enery_ratio}')
        Fl_Fp = 20*np.log10(fault_indicators[0]/np.max(fault_indicators[1:]))
        print(f'Fl_Fp={Fl_Fp}') #  <48, <42, <36,  <30,----
        rotor_broken_bars_level = 0
        for  i,v in enumerate(model_para):
            if Fl_Fp >= v:
                rotor_broken_bars_level = i
                break
        LOG.info(f'rotor_broken_bars_level Fl_Fp={rotor_broken_bars_level}')

        # if model_para !=-1 :#
        #     res = 0
        #     while res < len(model_para) and rotor_eccentric_enery_ratio >= model_para[res] :
        #         res += 1
        #     rotor_eccentric_level = res
        # else:
        #     rotor_eccentric_level = 0
        # rotor_broken_bars_freq_values = fault_indicators
        rotor_broken_bars_freq_values = {'转子断条边带':fault_indicators}
        if rotor_broken_bars_level:
            rotor_broken_bars_possibility = 0.6 - 0.4/model_para[0]*(Fl_Fp-model_para[0])   
        
        return rotor_broken_bars_level,Fl_Fp,rotor_broken_bars_freq_values,rotor_broken_bars_possibility

