'''
电流故障诊断测试:20250214
stator_core 铁芯故障 STATORCORE
'''
import numpy as np
import os
import sys
import ast
import pywt
from scipy import signal
from scipy.fft import fft
CURRENT_PATH = os.path.dirname(os.path.realpath(__file__))
sys.path.append(os.path.join(CURRENT_PATH, '..'))
import warnings
warnings.filterwarnings('ignore')   
from log.logger import LOG
from mechanism.mm_parent import MM_PARENT


class STATORCORE(MM_PARENT):
    def __init__(self, args):   #
        super(STATORCORE, self).__init__(args)
        self.fs = self.args.fs  # 采样频率sampling_rate
        self.power_freq = 50  # 电源频率(Hz)
        self.p = self.args.p # 级数 
        self.rotor_speed = self.args.speed

    def fft_analysis(self, signal):
        """FFT频谱分析"""
        N = len(signal)
        freq = np.fft.fftfreq(N, 1/self.fs)
        fft_result = 2*np.abs(fft(signal))/N   # np.abs(fft(signal)) 
        return freq[:N//2], fft_result[:N//2]
    
    def diagnosis(self,current):
        """铁芯故障诊断"""
        freq, spectrum = self.fft_analysis(current)
        fr = self.rotor_speed / 60  # 转子频率
        stator_core_possibility = 0
        model_para = self.args.model_fault_ratio 
        if model_para != -1:
            model_para = np.linspace(model_para,1,5).tolist()[:-1]  
        # loose_set_lines = ast.literal_eval(self.args.model_fault_set_para).get('loose_structure_lines',-1)

        wavelet = 'db4'
        level = 5
        coeffs = pywt.wavedec(self.current, wavelet, level=level)
        # 分析高频系数
        high_freq_energy = np.sum(coeffs[1]**2)
        total_energy = sum(np.sum(c**2) for c in coeffs)
        stator_core_enery_ratio = high_freq_energy/total_energy
        LOG.info(f'stator_core_enery_ratio={stator_core_enery_ratio}')
        if model_para !=-1 :#
            res = 0
            while res < len(model_para) and stator_core_enery_ratio >= model_para[res] :
                res += 1
            stator_core_level = res
        else:
            stator_core_level = 0
        # stator_core_freq_values = high_freq_energy
        stator_core_freq_values = {'铁芯故障':high_freq_energy}
        if stator_core_level:
            stator_core_possibility = 0.6 + 0.4/(1-model_para[0])*(stator_core_enery_ratio-model_para[0])
        
        return stator_core_level,stator_core_enery_ratio,stator_core_freq_values,stator_core_possibility
    
