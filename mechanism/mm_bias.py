'''
diagnose_rotor_eccentricity
date:20250630,bias
'''
import numpy as np
import os
import sys
CURRENT_PATH = os.path.dirname(os.path.realpath(__file__))
sys.path.append(os.path.join(CURRENT_PATH, '..'))
from utils.scipy_fft import fft_plot
from utils.common_use import get_section_freq_max,get_max_fvalues_speednX_value # 
from log.logger import LOG


from mechanism.mm_parent import MM_PARENT


class BIAS(MM_PARENT):
    def __init__(self, args):
        super(BIAS, self).__init__(args)

    # 机理模型诊断
    def diagnosis(self, wave_array):
        x = wave_array
        # LOG.info(f'wave_array={wave_array}')
        fs = self.args.fs
        speed = self.args.speed
        LOG.info(f'fs={fs},speed={speed}')
        # model_unbalance_para = self.args.get('unbalance',-1)
        model_bias_para = self.args.model_fault_ratio 
        LOG.info(f'model_bias_para={model_bias_para}')
        if model_bias_para != -1:
            model_bias_para = np.linspace(model_bias_para,1,5).tolist()[:-1]  
        time_power = np.sum(np.power(x, 2))/fs
        # print(f'time_power={time_power}')
        bias = 0
        bias_level = 0
        speed_freq_possibility = 0
        N = len(x)
        deltf = fs / N
        speed_freq = speed/60
        freqs,psd,_angle = fft_plot(x, fs)
        # 检测1倍频和2倍频幅值
        fundamental_idx = np.argmin(np.abs(freqs - speed_freq))
        second_harmonic_idx = np.argmin(np.abs(freqs - 2*speed_freq))
        
        fundamental_amp = psd[fundamental_idx]
        second_harmonic_amp = psd[second_harmonic_idx]
        total_amp = np.sum(np.power(x, 2))/fs  #np.sum(psd) 
        
        # 计算1倍频占比和1/2倍频幅值比
        fundamental_ratio = fundamental_amp / total_amp
        harmonic_ratio = second_harmonic_amp / fundamental_amp
        
        # 判断准则
        is_eccentric = (fundamental_ratio > 0.3 and  # 1倍频能量占比超过30%
                        harmonic_ratio < 0.5 and harmonic_ratio > 0.1)         # 2倍频幅值小于1倍频的50%

        bias_enery = (fundamental_amp /2 *N)**2/N/fs*2 + (second_harmonic_amp /2 *N)**2/N/fs*2
        print(f'bias_enery:{bias_enery}')
        bias_enery_ratio = bias_enery/time_power
        LOG.info(f'bias_enery_ratio={bias_enery_ratio}')
        if model_bias_para != -1 and is_eccentric:
            res = 0
            while res < len(model_bias_para) and bias_enery_ratio >= model_bias_para[res]:
                res += 1
            bias_level = res
            LOG.info(bias_enery_ratio, res,model_bias_para)
            if res == 0:
                bias = 0
            else:
                bias = 1
        else:
            bias = 0
            bias_level = 0
        speed_freq_values = {'转频1X':fundamental_amp,'转频2X':second_harmonic_amp}
        if bias:
            speed_freq_possibility = 0.6 + 0.4/(1 - model_bias_para[0])*(bias_enery_ratio-model_bias_para[0]) #
        
        return bias_level,bias_enery_ratio,speed_freq_values,speed_freq_possibility
