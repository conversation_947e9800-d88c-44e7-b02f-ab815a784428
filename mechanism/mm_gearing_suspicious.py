'''
齿轮故障
'''
import os
import sys
import numpy as np
import pandas as pd
import ast
CURRENT_PATH = os.path.dirname(os.path.realpath(__file__))
sys.path.append(os.path.join(CURRENT_PATH, '..'))
from utils.scipy_fft import fft_plot,envelope_plot
from scipy.signal import decimate
from utils.common_use import get_section_freq_max
from mechanism.mm_parent import MM_PARENT
from log.logger import LOG



# 齿轮故障
class GEARING_FUZZY(MM_PARENT):
    def __init__(self, args):
        super(GEARING_FUZZY, self).__init__(args)

    # 机理模型诊断
    def diagnosis(self, wave_array):
        data1 = wave_array
        fs = self.args.fs
        N = len(data1)
        model_gearing_para = self.args.model_fault_ratio 
        if model_gearing_para != -1:
            model_gearing_para = np.linspace(model_gearing_para,1,5).tolist()[:-1]  
        
        threshold_max_f = ast.literal_eval(self.args.model_fault_set_para).get('threshold_max_f',-1)
        threshold_side_bands = ast.literal_eval(self.args.model_fault_set_para).get('threshold_side_bands',-1)
        envelope_fuzzy = ast.literal_eval(self.args.model_fault_set_para).get('envelope_fuzzy',-1)
        envelope_fuzzy_value = ast.literal_eval(self.args.model_fault_set_para).get('envelope_fuzzy_value',-1)
        time_power = np.sum(np.power(data1, 2))/fs

        max_loc_f_envelope,max_value_envelope = get_envelope_max_f(data1,fs,envelope_fuzzy)
        # print(f'0~100,max_loc_f_envelope = {max_loc_f_envelope},max_value_envelope={max_value_envelope}') # 
        if max_value_envelope> envelope_fuzzy_value:#0.5
            flag_fault,center_fault,v1,v2,band_f,v3 = hps(data1,fs,max_loc_f_envelope,max_value_envelope,threshold_max_f,threshold_side_bands)
        else:
            flag_fault = 0
            center_fault = 0
            v1=0
            v2=0
            band_f=0
            v3=0
        # print(f'flag_fault={flag_fault},center_fault={center_fault},v1={v1},v2={v2},band_f={band_f},v3={v3}')
        # convert: loose_structure_level,loose_enery_ratio,speed_freq_values,speed_freq_possibility
        gearing_enery = 4*(v2 /2 *N)**2/N/fs*2  + 24*(v3 /2 *N)**2/N/fs*2
        gearing_enery_ratio = gearing_enery / time_power
        LOG.info(f'gearing_enery_ratio={gearing_enery_ratio}')
        # print(f'loose_enery={loose_enery},loose_enery_ratio={loose_enery_ratio}')

        if model_gearing_para !=-1 :#
            res = 0
            while res < len(model_gearing_para) and gearing_enery_ratio >= model_gearing_para[res] :
                res += 1
            # print(loose_enery_ratio, res,model_loose_para)
            gearing_structure_level = res
        else:
            gearing_structure_level = 0
        # gearing_freq_values = [v1,v2,v3]
        # gearing_freq_values = [flag_fault,center_fault,v1,v2,band_f,v3]
        gearing_freq_values = {'结构存在':flag_fault,'中心故障频率':center_fault,'故障基频幅值':v1,'故障平均幅值':v2,'故障边频':band_f,'边频幅值':v3}
        if gearing_structure_level:
            gearing_freq_possibility = 0.6 + 0.4/(1-model_gearing_para[0])*(gearing_enery_ratio-model_gearing_para[0])
        else:
            gearing_freq_possibility = 0
        
        return gearing_structure_level,gearing_enery_ratio,gearing_freq_values,gearing_freq_possibility
        # return flag_fault,center_fault,v1,v2,band_f,v3


 #get side band 
def get_envelope_max_f(data1,fs,envelope_fuzzy):
    N = len(data1)
    norm_x = data1 - np.mean(data1)
    freq_x, real_amp,_a = fft_plot(norm_x,fs)
    freq_x2, real_amp2 = envelope_plot(norm_x,fs)
    max_find_point = int(envelope_fuzzy*N/fs)
    max_value_envelope = max(real_amp2[:max_find_point])
    max_loc_point = np.argmax(real_amp2[:max_find_point])
    max_loc_f_envelope = max_loc_point/N*fs
    # print(f'max_f in envelope:{max_loc_f_envelope},max_value = {max_value_envelope}')
    return max_loc_f_envelope,max_value_envelope       


def hps(data1,fs,max_loc_f_envelope,max_value_envelope,threshold_max_f,threshold_side_bands):
    N = len(data1)
    deltf = fs / N
    freq_x, real_amp,_a = fft_plot(data1,fs)
    y2 = decimate(real_amp, 2)
    y3 = decimate(real_amp, 3)
    # y4 = decimate(real_amp, 4)
    # y5 = decimate(real_amp, 5)
    min_len = len(y3)
    yy = real_amp[:min_len]  * y2[:min_len] * y3[:min_len] #* y4[:min_len] # * y5[:min_len]
    sorted_id = sorted(range(len(yy)), key=lambda k: yy[k], reverse=True)
    # result_max_f = freq_x[sorted_id[:15]]
    # result_mul_value = yy[sorted_id[:15]]
    result_real_value = real_amp[sorted_id[:15]]
    max_hps_pos1 = freq_x[sorted_id[0]] + max_loc_f_envelope
    max_hps_pos2 = freq_x[sorted_id[0]] + 2* max_loc_f_envelope
    max_hps_pos3 = freq_x[sorted_id[0]] + 3* max_loc_f_envelope
    max_hps_neg1 = freq_x[sorted_id[0]] - max_loc_f_envelope
    max_hps_neg2 = freq_x[sorted_id[0]] - 2 * max_loc_f_envelope
    max_hps_neg3 = freq_x[sorted_id[0]] - 3 * max_loc_f_envelope
    bands_f_array = [max_hps_pos1,max_hps_pos2,max_hps_pos3,max_hps_neg1,max_hps_neg2,max_hps_neg3] # center 1X +-3band
    bands_f2_array = [i + freq_x[sorted_id[0]] for i in bands_f_array]
    bands_f3_array = [i + freq_x[sorted_id[0]] for i in bands_f2_array]
    bands_f4_array = [i + freq_x[sorted_id[0]] for i in bands_f3_array]
    center_freq_array = [freq_x[sorted_id[0]],2* freq_x[sorted_id[0]],3*freq_x[sorted_id[0]],4*freq_x[sorted_id[0]]]
    max_center_avg_value = 0
    for fi in center_freq_array[1:]:
        pos1_left = int((fi - 3*deltf) * N / fs)
        pos1_right = int((fi + 3*deltf) * N / fs)
        if fi - 3*deltf > 0 and fi + 3*deltf < fs/2.56:
            max_hps_pos1_real, max_hps_pos1_max_fvalues,_index =  get_section_freq_max(real_amp,freq_x, pos1_left, pos1_right)
            max_center_avg_value += max_hps_pos1_max_fvalues
        else:
            max_center_avg_value += 0
    max_center_avg_value += result_real_value[0]
    max_center_avg_value /= 4
    side_band1_avg_value = 0
    side_band2_avg_value = 0
    side_band3_avg_value = 0
    side_band4_avg_value = 0
    side_band_avg_value = 0
    for fi in bands_f_array:
        pos1_left = int((fi - 3*deltf) * N / fs)
        pos1_right = int((fi + 3*deltf) * N / fs)
        if fi - 3*deltf > 0 and fi + 3*deltf < fs/2.56:
            max_hps_pos1_real, max_hps_pos1_max_fvalues,_index =  get_section_freq_max(real_amp,freq_x, pos1_left, pos1_right)
            side_band1_avg_value += max_hps_pos1_max_fvalues
        else:
            side_band1_avg_value += 0
    side_band1_avg_value /= 6
    # print(f'side_band_avg_value ={side_band1_avg_value}')
    for fi in bands_f2_array:
        pos1_left = int((fi - 3*deltf) * N / fs)
        pos1_right = int((fi + 3*deltf) * N / fs)
        if fi - 3*deltf > 0 and fi + 3*deltf < fs/2.56:
            max_hps_pos1_real, max_hps_pos1_max_fvalues,_index =  get_section_freq_max(real_amp,freq_x, pos1_left, pos1_right)
            side_band2_avg_value += max_hps_pos1_max_fvalues
        else:
            side_band2_avg_value += 0
    side_band2_avg_value /= 6
    for fi in bands_f3_array:
        pos1_left = int((fi - 3*deltf) * N / fs)
        pos1_right = int((fi + 3*deltf) * N / fs)
        if fi - 3*deltf > 0 and fi + 3*deltf < fs/2.56:
            max_hps_pos1_real, max_hps_pos1_max_fvalues,_index =  get_section_freq_max(real_amp,freq_x, pos1_left, pos1_right)
            side_band3_avg_value += max_hps_pos1_max_fvalues
        else:
            side_band3_avg_value += 0
    side_band3_avg_value /= 6
    for fi in bands_f4_array:
        pos1_left = int((fi - 3*deltf) * N / fs)
        pos1_right = int((fi + 3*deltf) * N / fs)
        if fi - 3*deltf > 0 and fi + 3*deltf < fs/2.56:
            max_hps_pos1_real, max_hps_pos1_max_fvalues,_index =  get_section_freq_max(real_amp,freq_x, pos1_left, pos1_right)
            side_band4_avg_value += max_hps_pos1_max_fvalues
        else:
            side_band4_avg_value += 0
    side_band4_avg_value /= 6
    # print(f'max_center_avg_value={max_center_avg_value}')
    # print(f'side_band1_avg_value={side_band1_avg_value},side_band2_avg_value={side_band2_avg_value},side_band3_avg_value={side_band3_avg_value},side_band4_avg_value={side_band4_avg_value}')
    side_band_avg_value = (side_band1_avg_value + side_band2_avg_value + side_band3_avg_value + side_band4_avg_value)/4
    # print(f'side_band_avg_value={side_band_avg_value}')
    # print(f'返回基频幅值={result_real_value[0]},倍频平均幅值={max_center_avg_value},边带幅值={side_band_avg_value}') # 
    if max_center_avg_value > threshold_max_f or side_band_avg_value > threshold_side_bands:
        # print(f'side_band_avg_value beyond threshold_side_bands: structure exist!!!')
        return 1,freq_x[sorted_id[0]],result_real_value[0],max_center_avg_value,max_loc_f_envelope,side_band_avg_value
    else:
        # print(f'normal ok')
        return 0,freq_x[sorted_id[0]],result_real_value[0],max_center_avg_value,max_loc_f_envelope,side_band_avg_value








