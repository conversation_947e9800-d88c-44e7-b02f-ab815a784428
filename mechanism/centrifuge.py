'''
centrifuge:离心机堵料
'''
import numpy as np
from scipy import signal
import os
import ast
import sys
CURRENT_PATH = os.path.dirname(os.path.realpath(__file__))
sys.path.append(os.path.join(CURRENT_PATH, '..'))
import matplotlib
matplotlib.use('TkAgg')
import matplotlib.pyplot as plt
plt.rcParams['font.sans-serif']=['SimHei'] #用来正常显示中文标签
plt.rcParams['axes.unicode_minus']=False #用来正常显示负号
from log.logger import LOG
from utils.scipy_fft import fft_plot,envelope_plot


class CentrifugeFaultSimulator:
    def __init__(self, sampling_rate=5120, duration=1.0):
        """
        初始化离心机故障模拟器
        sampling_rate: 采样率
        duration: 信号持续时间(秒)
        """
        self.sampling_rate = sampling_rate
        self.duration = duration
        self.time = np.linspace(0, duration, int(sampling_rate * duration))
        
    def generate_screen_clogging(self, rotor_freq=25, severity=0.5, load_variation=0.4):
        """
        模拟筛蓝堵料故障
        rotor_freq: 转子频率
        severity: 堵料严重程度
        load_variation: 负载波动程度
        """
        # 基础转子信号
        base_signal = np.sin(2 * np.pi * rotor_freq * self.time)
        
        # 模拟不平衡负载导致的1X分量增大
        unbalance = severity * np.sin(2 * np.pi * rotor_freq * self.time)
        
        # 模拟负载波动导致的低频调制
        load_freq = 0.5  # 负载波动频率
        load_modulation = 1 + load_variation * np.sin(2 * np.pi * load_freq * self.time)
        
        # 模拟冲击响应
        impact_freq = rotor_freq / 3  # 每转3圈产生一次冲击
        impacts = np.zeros_like(self.time)
        impact_interval = int(self.sampling_rate / impact_freq)
        impacts[::impact_interval] = severity
        
        # 模拟冲击响应的衰减
        decay = np.exp(-10 * np.linspace(0, 1, impact_interval))
        impact_response = np.convolve(impacts, decay, mode='same')
        
        # 组合所有特征
        signal = (base_signal + unbalance) * load_modulation + impact_response
        
        # 添加随机噪声
        noise = np.random.normal(0, 0.1, len(self.time))
        
        return signal + noise

class CentrifugeDiagnostics:
    def __init__(self,args):
        super(CentrifugeDiagnostics, self).__init__(args)
        # """初始化诊断器"""
        # self.fault_thresholds = {
        #     'clogging': {
        #         'load_variation': 0.3,     # 负载波动阈值
        #         'impact_energy': 0.25,     # 冲击能量阈值
        #         'unbalance_ratio': 0.4,    # 不平衡比值阈值
        #         'low_freq_energy': 0.2     # 低频能量阈值
        #     }
        # }
    
    def diagnose_screen_clogging(self, data, sampling_rate):
        """
        诊断筛蓝堵料故障
        返回故障特征和严重程度
        """
        model_fault_ratio1 = self.args.model_fault_ratio #
        if model_fault_ratio1 != -1:
            model_fault_ratio1 = np.linspace(model_fault_ratio1,1,5).tolist()[:-1] 

        # low_freq_energy1 = ast.literal_eval(self.args.model_fault_set_para).get('low_freq_energy',-1)#low_freq_energy
        # impact_energy1 = ast.literal_eval(self.args.model_fault_set_para).get('impact_energy',-1)#impact_energy
        # unbalance_ratio1 = ast.literal_eval(self.args.model_fault_set_para).get('unbalance_ratio',-1)#
        # 计算频谱
        freq, spectrum = self.calculate_spectrum(data, sampling_rate)
        # 计算包络谱
        env_freq, env_spectrum = self.calculate_envelope_spectrum(data, sampling_rate)
        # 1. 分析负载波动（低频调制）
        low_freq_idx = freq < 1  # 1Hz以下的低频成分
        low_freq_energy = np.sum(spectrum[low_freq_idx]) / np.sum(spectrum)
        LOG.info(f'low_freq_energy={low_freq_energy}')
        # 2. 分析冲击特征
        impact_energy = np.sum(env_spectrum) / len(env_spectrum)
        LOG.info(f'impact_energy={impact_energy}')
        # 3. 计算不平衡度（1X分量）
        fundamental_idx = np.argmax(spectrum[:len(spectrum)//4])
        fundamental_amp = spectrum[fundamental_idx]
        # baseline_amp = np.mean(spectrum[:len(spectrum)//4])
        baseline_amp = np.sum(spectrum[:len(spectrum)//4])
        unbalance_ratio = fundamental_amp / baseline_amp
        LOG.info(f'unbalance_ratio={unbalance_ratio}')
        # 4. 计算调制度
        # modulation_index = np.max(env_spectrum[1:]) / np.mean(env_spectrum[1:])#env_spectrum[0]
        modulation_index = np.max(env_spectrum[1:]) / np.sum(env_spectrum[1:])#env_spectrum[0]
        LOG.info(f'env_spectrum_max={np.max(env_spectrum[1:])},modulation_index={modulation_index}')
        # 判断是否存在堵料故障
        clogging_values = {'低频调制':low_freq_energy,'冲击特征':impact_energy,'不平衡度':unbalance_ratio,'调制度':modulation_index}
        clogging_ratio = (low_freq_energy + impact_energy + unbalance_ratio + modulation_index)/4
        LOG.info(f'clogging_ratio={clogging_ratio}')
        clogging_possibility = 0
        if model_fault_ratio1 !=-1 :#
            res = 0
            while res < len(model_fault_ratio1) and clogging_ratio >= model_fault_ratio1[res] :
                res += 1
            clogging_level = res
        else:
            clogging_level = 0
        if clogging_level:
            clogging_possibility = 0.6 + 0.4/(1-model_fault_ratio1[0])*(clogging_ratio-model_fault_ratio1[0])
        return clogging_level,clogging_ratio,clogging_values,clogging_possibility

    
    def calculate_spectrum(self, data, sampling_rate):
        """计算频谱"""
        # spectrum = np.fft.fft(data)
        # freq = np.fft.fftfreq(len(data), 1/sampling_rate)
        freq_x,real_amp,_angle = fft_plot(data, sampling_rate)
        return freq_x, real_amp
    
    
    def calculate_envelope_spectrum(self, data, sampling_rate):
        """计算包络谱"""
        # analytic_signal = signal.hilbert(data)
        # envelope = np.abs(analytic_signal)
        # envelope_spectrum = np.fft.fft(envelope)
        # freq = np.fft.fftfreq(len(data), 1/sampling_rate)
        freq_x, real_amp = envelope_plot(data, sampling_rate)
        return freq_x, real_amp



#---离心机---
class CENTRIFUGE:
    def __init__(self):
        self.fault_types = {
            "CLOGGING": "堵塞"
        }
    
    @classmethod
    def diagnosis(cls, args):
        fs = args.fs
        vibration_data = args.wave_array

        result = []
        
        # 创建诊断实例
        vfd = CentrifugeDiagnostics(args) #
        LOG.info(f'args.model_type={args.model_type}') 
        # 执行各类故障检测
        if args.model_type == 'CLOGGING':
            result = vfd.diagnose_screen_clogging(vibration_data, fs)

        LOG.info(f'result={result}')
        return result


def plot_clogging_diagnosis(time, data, sampling_rate, diagnosis_result):
    """绘制堵料诊断结果"""
    plt.figure(figsize=(15, 10))
    
    # 1. 时域波形
    plt.subplot(411)
    plt.plot(time, data)
    plt.title('时域波形')
    plt.xlabel('时间 (s)')
    plt.ylabel('幅值')
    plt.grid(True)
    
    # 2. 频谱
    # freq = np.fft.fftfreq(len(data), 1/sampling_rate)
    # spectrum = np.fft.fft(data)
    freq,spectrum,_angle = fft_plot(data, sampling_rate)
    plt.subplot(412)
    plt.plot(freq[:len(freq)//2], np.abs(spectrum)[:len(spectrum)//2])
    plt.title('频谱')
    plt.xlabel('频率 (Hz)')
    plt.ylabel('幅值')
    plt.grid(True)
    
    # 3. 包络谱
    # analytic_signal = signal.hilbert(data)
    # envelope = np.abs(analytic_signal)
    # envelope_spectrum = np.fft.fft(envelope)
    freq, envelope_spectrum = envelope_plot(data, sampling_rate)
    plt.subplot(413)
    plt.plot(freq[:len(freq)//2], np.abs(envelope_spectrum)[:len(envelope_spectrum)//2])
    plt.title('包络谱')
    plt.xlabel('频率 (Hz)')
    plt.ylabel('幅值')
    plt.grid(True)
    
    # 4. 特征指标
    plt.subplot(414)
    features = diagnosis_result['features']
    names = list(features.keys())
    values = list(features.values())
    plt.bar(names, values)
    plt.title('故障特征指标')
    plt.xticks(rotation=45)
    plt.grid(True)
    
    plt.tight_layout()
    plt.show()

# 使用示例
if __name__ == '__main__':
    pass
    # # 创建模拟器和诊断器
    # simulator = CentrifugeFaultSimulator(sampling_rate=5120, duration=2.0)
    # diagnostics = CentrifugeDiagnostics()
    
    # # 生成不同严重程度的堵料故障数据
    # severities = [0.2, 0.5, 0.8]
    # for severity in severities:
    #     print(f"\n=== 堵料严重程度 {severity:.1f} 的诊断结果 ===")
        
    #     # 生成故障数据
    #     data = simulator.generate_screen_clogging(severity=severity)
        
    #     # 诊断
    #     result = diagnostics.diagnose_screen_clogging(data, simulator.sampling_rate)
        
    #     # 显示结果
    #     print(f"是否存在堵料: {'是' if result['is_clogging'] else '否'}")
    #     print(f"故障严重程度: {result['severity']:.2f}")
    #     print("\n特征值:")
    #     for name, value in result['features'].items():
    #         print(f"{name}: {value:.3f}")
        
    #     # 绘制诊断图表
    #     plot_clogging_diagnosis(simulator.time, data, simulator.sampling_rate, result) 