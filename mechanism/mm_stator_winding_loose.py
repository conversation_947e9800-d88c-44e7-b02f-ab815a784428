'''
电流故障诊断测试:20250214
stator_winding_loose 定子绕组松动 STATORWINDINGLOOSE

'''
import numpy as np
import os
import sys
import ast
from scipy.fft import fft
CURRENT_PATH = os.path.dirname(os.path.realpath(__file__))
sys.path.append(os.path.join(CURRENT_PATH, '..'))
import warnings
warnings.filterwarnings('ignore')   
from log.logger import LOG
from mechanism.mm_parent import MM_PARENT


class STATORWINDINGLOOSE(MM_PARENT):
    def __init__(self, args):   #args sampling_rate, current_signal,p
        super(STATORWINDINGLOOSE, self).__init__(args)
        self.fs = self.args.fs  # 采样频率sampling_rate
        self.power_freq = 50  # 电源频率(Hz)
        self.p = self.args.p # 级数 

    def fft_analysis(self, signal):
        """FFT频谱分析"""
        N = len(signal)
        freq = np.fft.fftfreq(N, 1/self.fs)
        fft_result = 2*np.abs(fft(signal))/N   # np.abs(fft(signal)) 
        return freq[:N//2], fft_result[:N//2]
    
    def diagnosis(self,current):
        """定子绕组松动故障诊断"""
        model_loose_para = self.args.model_fault_ratio 
        if model_loose_para != -1:
            model_loose_para = np.linspace(model_loose_para,1,5).tolist()[:-1]  
        # loose_set_lines = ast.literal_eval(self.args.model_fault_set_para).get('loose_structure_lines',-1)
        stator_winding_loose_possibility = 0
        freq, spectrum = self.fft_analysis(current)
        # freq, spectrum = self.freq, self.spectrum
        time_power = np.sum(np.power(current, 2))/self.fs
        N = len(current)
        deltf = self.fs/N
        target_freq = 2 * self.power_freq
        freq_range = 3*deltf  # 搜索范围±2Hz
        mask = (freq >= target_freq - freq_range) & (freq <= target_freq + freq_range)
        peak_amp = np.max(spectrum[mask])
        #--
        stator_winding_loose_enery =(peak_amp /2 *N)**2/N/self.fs*2
        LOG.info(f'stator_winding_loose_enery={stator_winding_loose_enery}')
        stator_winding_loose_enery_ratio = stator_winding_loose_enery / time_power
        LOG.info(f'stator_winding_loose_enery_ratio={stator_winding_loose_enery_ratio}')
        # 设定故障判断阈值
        if model_loose_para !=-1 :#
            res = 0
            while res < len(model_loose_para) and stator_winding_loose_enery_ratio >= model_loose_para[res] :
                res += 1
            stator_winding_loose_level = res
        else:
            stator_winding_loose_level = 0
        # stator_winding_loose_freq_values = [peak_amp]
        stator_winding_loose_freq_values = {'100HZ':1.1}
        if stator_winding_loose_level:
            stator_winding_loose_possibility = 0.6 + 0.4/(1-model_loose_para[0])*(stator_winding_loose_enery_ratio-model_loose_para[0])
        return stator_winding_loose_level,stator_winding_loose_enery_ratio,stator_winding_loose_freq_values,stator_winding_loose_possibility
    
    
