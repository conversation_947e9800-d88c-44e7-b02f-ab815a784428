'''

'''
import numpy as np
import os
import sys
import ast
CURRENT_PATH = os.path.dirname(os.path.realpath(__file__))
sys.path.append(os.path.join(CURRENT_PATH, '..'))
from utils.scipy_fft import fft_plot,envelope_plot
from utils.common_use import get_section_freq_max,get_multi_freq_amplitide,get_max_fvalues_speednX_value
from log.logger import LOG
from decimal import Decimal


from mechanism.mm_parent import MM_PARENT


class BEARING_BPFI(MM_PARENT):
    def __init__(self, args):
        super(BEARING_BPFI, self).__init__(args)

    # 机理模型诊断
    def diagnosis(self, wave_array):
        x = wave_array
        fs = self.args.fs
        speed = self.args.speed
        bearing_parameter = self.args.bearing_parameter
        model_bearing_BPFI_para = self.args.model_fault_ratio
        
        if model_bearing_BPFI_para != -1:
            model_bearing_BPFI_para = np.linspace(model_bearing_BPFI_para,1,5).tolist()[:-1]  
        BPFI_set_lines = ast.literal_eval(self.args.model_fault_set_para).get('BPFI_structure_lines',-1)
        bearing_env = ast.literal_eval(self.args.model_fault_set_para).get('bearing_env',-1)


        time_power = np.sum(np.power(x, 2))/fs
        # print(f'time_power={time_power}')
        # print(f'type:bearing_parameter={type(bearing_parameter)}')
        bearing_size = int(len(bearing_parameter)/4)
        bearing_parameter = [float(i) for i in bearing_parameter]
        bearing_freq_values = [] # pinyu 4 + baol4 * 4buwei * zhoucheng geshu, 4[]*n
        bearing_freq_possibility = 0# 
        # possibility
        bearing_freq_BPFI_possibility = np.zeros(bearing_size,dtype=float)
        bearing_freq_BPFO_possibility = np.zeros(bearing_size,dtype=float)
        bearing_freq_BSF_possibility = np.zeros(bearing_size,dtype=float)
        bearing_freq_FTF_possibility = np.zeros(bearing_size,dtype=float)

        # print(f'bearing_parameter[0] type ={type(bearing_parameter[0])},bearing_parameter[0]={bearing_parameter[0]}')
        if bearing_parameter is  None or len(bearing_parameter) == 0 :
            return 0,0,[],0,0

        N = len(x)
        deltf = fs / N
        speed_freq = speed/60
        freq_x,real_amp,_a = fft_plot(x, fs)
        freq_x_envelope,real_amp_envelope = envelope_plot(x,fs)
        # bearing_size = int(len(bearing_parameter)/4)
        f_BPFI = np.zeros(bearing_size,dtype=float)
        f_BPFO = np.zeros(bearing_size,dtype=float)
        f_BSF = np.zeros(bearing_size,dtype=float)
        f_FTF = np.zeros(bearing_size,dtype=float)
        bearing_BPFI = np.zeros(bearing_size,dtype=int)
        bearing_BPFI_level = np.zeros(bearing_size,dtype=int)
        bearing_BPFO = np.zeros(bearing_size,dtype=int)
        bearing_BPFO_level = np.zeros(bearing_size,dtype=int)
        bearing_BSF = np.zeros(bearing_size,dtype=int)
        bearing_BSF_level = np.zeros(bearing_size,dtype=int)
        bearing_FTF = np.zeros(bearing_size,dtype=int)
        bearing_FTF_level = np.zeros(bearing_size,dtype=int)
        #--20241021--enery-temp-save--
        bearing_BPFI_enery_array =  np.zeros(bearing_size,dtype=float)
        bearing_BPFI_enery_env_array =  np.zeros(bearing_size,dtype=float)

        bearing_BPFO_enery_array =  np.zeros(bearing_size,dtype=float)
        bearing_BPFO_enery_env_array =  np.zeros(bearing_size,dtype=float)

        bearing_BSF_enery_array =  np.zeros(bearing_size,dtype=float)
        bearing_BSF_enery_env_array =  np.zeros(bearing_size,dtype=float)

        bearing_FTF_enery_array =  np.zeros(bearing_size,dtype=float)
        bearing_FTF_enery_env_array =  np.zeros(bearing_size,dtype=float)


        fault_index = 0
        for i in range(bearing_size):
            BPFI = bearing_parameter[i*4]
            BPFO = bearing_parameter[i*4+1]
            BSF = bearing_parameter[i*4+2]
            FTF = bearing_parameter[i*4+3]
            f_BPFI[i] = speed_freq * BPFI
            # print(f'speed_freq={speed_freq},type:{type(speed_freq)},BPFI={BPFI},type:{type(BPFI)}')
            f_BPFO[i] = speed_freq * BPFO
            f_BSF[i] = speed_freq * BSF
            f_FTF[i] = speed_freq * FTF
        # print(f'f_BPFI = {f_BPFI},f_BPFO = {f_BPFO},f_BSF = {f_BSF}, f_FTF = {f_FTF}')
        # bearing_BPFI_enery = 0
        # bearing_BPFO_enery = 0
        # bearing_BSF_enery = 0
        # bearing_FTF_enery = 0
        bearing_enery_env_all = np.sum((real_amp_envelope/2*N)**2/N/fs*2)
        bearing_BPFI,bearing_BPFI_level,fault_index,bearing_freq_possibility,bearing_freq_values_bpfi,bearing_BPFI_enery_env_ratio = bearing_element_calculate(bearing_size,fs,f_BPFI,real_amp,freq_x,N,deltf,real_amp_envelope,freq_x_envelope,time_power,bearing_enery_env_all,model_bearing_BPFI_para,BPFI_set_lines,bearing_env)
        # print(f'bearing_BPFI_level={bearing_BPFI_level}')
        # # print(f'bearing_BPFI={bearing_BPFI},bearing_BPFI_level={bearing_BPFI_level},fault_index={fault_index},bearing_freq_possibility={bearing_freq_possibility}')
        # bearing_BPFO,bearing_BPFO_level,fault_index[1],bearing_freq_possibility[1],bearing_freq_values_bpfo = bearing_element_calculate(bearing_size,fs,f_BPFO,real_amp,freq_x,N,deltf,real_amp_envelope,freq_x_envelope,time_power,bearing_enery_env_all,model_bearing_BPFO_para,BPFO_set_lines,bearing_env)
        # # print(f'bearing_BPFO={bearing_BPFO},bearing_BPFO_level={bearing_BPFO_level},fault_index={fault_index},bearing_freq_possibility={bearing_freq_possibility}')
        # bearing_BSF,bearing_BSF_level,fault_index[2],bearing_freq_possibility[2],bearing_freq_values_bsf = bearing_element_calculate(bearing_size,fs,f_BSF,real_amp,freq_x,N,deltf,real_amp_envelope,freq_x_envelope,time_power,bearing_enery_env_all,model_bearing_BSF_para,BSF_set_lines,bearing_env)
        # # print(f'bearing_BSF={bearing_BSF},bearing_BSF_level={bearing_BSF_level},fault_index={fault_index},bearing_freq_possibility={bearing_freq_possibility}')
        # bearing_FTF,bearing_FTF_level,fault_index[3],bearing_freq_possibility[3],bearing_freq_values_ftf = bearing_element_calculate(bearing_size,fs,f_FTF,real_amp,freq_x,N,deltf,real_amp_envelope,freq_x_envelope,time_power,bearing_enery_env_all,model_bearing_FTF_para,FTF_set_lines,bearing_env)
        # # print(f'bearing_FTF={bearing_FTF},bearing_FTF_level={bearing_FTF_level},fault_index={fault_index},bearing_freq_possibility={bearing_freq_possibility}')
        # bearing_freq_values.extend(bearing_freq_values_bpfi)
        if bearing_size == 1:
            bearing_freq_values = {'内圈特征频率1X':bearing_freq_values_bpfi[0],'内圈特征频率2X':bearing_freq_values_bpfi[1],'内圈特征频率3X':bearing_freq_values_bpfi[2],'"内圈特征频率4X"':bearing_freq_values_bpfi[3],'内圈特征频率包络1X':bearing_freq_values_bpfi[4],'内圈特征频率包络2X':bearing_freq_values_bpfi[5],'内圈特征频率包络3X':bearing_freq_values_bpfi[6],'内圈特征频率包络4X':bearing_freq_values_bpfi[7]}
        else:
            bearing_freq_values_bpfi = np.average(bearing_freq_values_bpfi, axis=0)
            bearing_freq_values = {'内圈特征频率1X':bearing_freq_values_bpfi[0],'内圈特征频率2X':bearing_freq_values_bpfi[1],'内圈特征频率3X':bearing_freq_values_bpfi[2],'"内圈特征频率4X"':bearing_freq_values_bpfi[3],'内圈特征频率包络1X':bearing_freq_values_bpfi[4],'内圈特征频率包络2X':bearing_freq_values_bpfi[5],'内圈特征频率包络3X':bearing_freq_values_bpfi[6],'内圈特征频率包络4X':bearing_freq_values_bpfi[7]}

        #[max_fvalues_main1X,max_fvalues_main2X,max_fvalues_main3X,max_fvalues_main4X,max_fvalues_main1X_env,max_fvalues_main2X_env,max_fvalues_main3X_env,max_fvalues_main4X_env]
        # bearing_freq_values.extend(bearing_freq_values_bpfo)
        # bearing_freq_values.extend(bearing_freq_values_bsf)
        # bearing_freq_values.extend(bearing_freq_values_ftf)

        return bearing_BPFI_level,bearing_BPFI_enery_env_ratio,bearing_freq_values,bearing_freq_possibility,fault_index


def bearing_element_calculate(bearing_size,fs,f_BPFI,real_amp,freq_x,N,deltf,real_amp_envelope,freq_x_envelope,time_power,bearing_enery_env_all,model_bearing_BPFI_para,BPFI_set_lines,bearing_env):
    bearing_BPFI_enery_array =  np.zeros(bearing_size,dtype=float)
    bearing_BPFI_enery_env_array =  np.zeros(bearing_size,dtype=float)
    bearing_freq_values = []
    fault_index = 0
    bearing_freq_possibility = 0
    for ii in range(bearing_size):
        _f_BPFI,max_fvalues_main1X, max_fvalues_main2X, max_fvalues_main3X, max_fvalues_main4X = get_multi_freq_amplitide(fs, f_BPFI[ii],real_amp, freq_x, N, deltf) 
        _f_BPFI_env,max_fvalues_main1X_env, max_fvalues_main2X_env, max_fvalues_main3X_env, max_fvalues_main4X_env = get_multi_freq_amplitide(fs, f_BPFI[ii],real_amp_envelope, freq_x_envelope, N, deltf) 
        bearing_BPFI_enery_temp =  (max_fvalues_main1X /2 *N)**2/N/fs*2 + (max_fvalues_main2X /2 *N)**2/N/fs*2 + (max_fvalues_main3X /2 *N)**2/N/fs*2 + (max_fvalues_main4X /2 *N)**2/N/fs*2
        # print(f'before set line: bearing_BPFI_enery_temp={bearing_BPFI_enery_temp}')
        bearing_BPFI_enery_env =  (max_fvalues_main1X_env /2 *N)**2/N/fs*2 + (max_fvalues_main2X_env /2 *N)**2/N/fs*2 + (max_fvalues_main3X_env /2 *N)**2/N/fs*2 + (max_fvalues_main4X_env /2 *N)**2/N/fs*2
        if BPFI_set_lines !=-1 and BPFI_set_lines > 4:
            # print(f'set more lines to BPFI_lines! set_lines={BPFI_set_lines}')
            for bpfi_i in range(5,BPFI_set_lines+1):
                # print(f'bpfi_i={bpfi_i}')
                BPFI_iX = _f_BPFI * bpfi_i
                #check 20241021,main_nX > fs/2.56
                if BPFI_iX > fs/2.56:
                    # print(f'BPFI_iX频率设置溢出采集范围:break loose_i!')
                    break
                max_fvalues_BPFIiX = get_max_fvalues_speednX_value(BPFI_iX,deltf,N,fs,real_amp,freq_x)
                BPFI_enery_i_temp = (max_fvalues_BPFIiX /2 *N)**2/N/fs*2
                # print(f'BPFI_enery_i_temp={BPFI_enery_i_temp}')
                #计算>4 loose lines enery sum；+ 1~4x；
                bearing_BPFI_enery_temp += BPFI_enery_i_temp
        # print(f'after setlines: bearing_BPFI_enery_temp={bearing_BPFI_enery_temp}')
        bearing_BPFI_enery_array[ii] = bearing_BPFI_enery_temp
        bearing_BPFI_enery_env_array[ii] = bearing_BPFI_enery_env
        bearing_freq_values.append([max_fvalues_main1X,max_fvalues_main2X,max_fvalues_main3X,max_fvalues_main4X,max_fvalues_main1X_env,max_fvalues_main2X_env,max_fvalues_main3X_env,max_fvalues_main4X_env])
    # print(f'------------求解所有轴承内圈得到的能量和----------')
    
    bearing_BPFI_enery_ratio = np.sum(bearing_BPFI_enery_array) / time_power
    bearing_BPFI_enery_env_ratio = np.sum(bearing_BPFI_enery_env_array) / bearing_enery_env_all
    LOG.info(f'bearing_enery_ratio={bearing_BPFI_enery_ratio} VS bearing_enery_env_ratio {bearing_BPFI_enery_env_ratio}')
    if model_bearing_BPFI_para != -1:
        res = 0
        while res < len(model_bearing_BPFI_para) and bearing_BPFI_enery_ratio >= model_bearing_BPFI_para[res] :
            res += 1
        print(bearing_BPFI_enery_ratio, res,model_bearing_BPFI_para)
        bearing_BPFI_level = res
        if res == 0:
            bearing_BPFI = 0
            # fault_index.append(0)
            fault_index = 0
            # print(f'包络:test---bearing_env:4')
            if bearing_env != -1:
                # print('设置了包络比较')
                if bearing_BPFI_enery_env_ratio > model_bearing_BPFI_para[0]:
                    # print(f'超出设置:bearing_BPFI_enery_env_ratio={bearing_BPFI_enery_env_ratio},model_bearing_BPFI_para[0]={model_bearing_BPFI_para[0]}')
                    bearing_BPFI_level = 1
                    bearing_BPFI = 1
                    # fault_index.append(np.argmax(bearing_BPFI_enery_env_array) + 1)
                    fault_index = np.argmax(bearing_BPFI_enery_env_array) + 1
                    bearing_freq_possibility = 0.6 + 0.4/(1 - model_bearing_BPFI_para[0])*(bearing_BPFI_enery_env_ratio-model_bearing_BPFI_para[0])
        else:
            # fault_index.append(np.argmax(bearing_BPFI_enery_array) + 1)
            fault_index = np.argmax(bearing_BPFI_enery_array) + 1
            bearing_BPFI = 1
            bearing_freq_possibility = 0.6 + 0.4/(1 - model_bearing_BPFI_para[0])*(bearing_BPFI_enery_ratio-model_bearing_BPFI_para[0])
            # print(f' BPFI, bearing_freq_possibility={bearing_freq_possibility}')
    else:
        bearing_BPFI = 0 
        bearing_BPFI_level = 0
    # print(f'bearing_BPFI={bearing_BPFI},bearing_BPFI_level={bearing_BPFI_level},fault_index={fault_index},bearing_freq_possibility={bearing_freq_possibility}')
    return bearing_BPFI,bearing_BPFI_level,fault_index,bearing_freq_possibility,bearing_freq_values,bearing_BPFI_enery_env_ratio





