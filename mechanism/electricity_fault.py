'''
date:20240805
electricity_fault:
1:定子绕组松动;
2:定子偏心;
3:铁芯故障；
4:转子偏心;
5:转子笼条断裂;

'''
import numpy as np
import os
import sys
CURRENT_PATH = os.path.dirname(os.path.realpath(__file__))
sys.path.append(os.path.join(CURRENT_PATH, '..'))
from utils.scipy_fft import fft_plot
from utils.common_use import get_section_freq_max #


def get_CF_LF(CF,LF,fs,N):
    main_1X = CF
    deltf = fs / N
    main_1Xwucha = 3*deltf
    CF_left = CF - main_1Xwucha
    CF_left = int(CF_left * N / fs)
    CF_right = CF + main_1Xwucha
    CF_right = int(CF_right * N / fs) 
    #
    LFpos1 = CF + LF
    LFneg1 = CF - LF
    deltf = fs / N
    main_1Xwucha = 3*deltf
    LFpos1_left = LFpos1 - main_1Xwucha
    LFpos1_left = int(LFpos1_left * N / fs)
    LFpos1_right = LFpos1 + main_1Xwucha
    LFpos1_right = int(LFpos1_right * N / fs) 
    # 
    LFneg1_left = LFneg1 - main_1Xwucha
    LFneg1_left = int(LFneg1_left * N / fs)
    LFneg1_right = LFneg1 + main_1Xwucha
    LFneg1_right = int(LFneg1_right * N / fs) 
    return LFneg1_left,LFneg1_right,CF_left,CF_right,LFpos1_left,LFpos1_right

#  1:stator winding Loose 100Hz 
def electricity_fault(x,y,z,fs,CF_stator_slot,CF_rotor_slot,LF,speed,PPF):
    fault_dict = {}
    #  1:stator winding Loose 100Hz 
    main_1X = 100
    N = len(x)
    deltf = fs / N
    freq_x1,real_amp1,_a = fft_plot(x, fs)
    freq_x1,real_amp2,_a = fft_plot(y, fs)
    freq_x1,real_amp3,_a = fft_plot(z, fs)
    main_1Xwucha = 3*deltf
    main_freq_left = main_1X - main_1Xwucha
    main_point_left = int(main_freq_left * N / fs)
    main_freq_right = main_1X + main_1Xwucha
    main_point_right = int(main_freq_right * N / fs) 
    max_f1,max_f100values1,max_value_loc1 = get_section_freq_max(real_amp1,freq_x1, main_point_left, main_point_right)
    max_f2,max_f100values2,max_value_loc2 = get_section_freq_max(real_amp2,freq_x1, main_point_left, main_point_right)
    max_f3,max_f100values3,max_value_loc3 = get_section_freq_max(real_amp3,freq_x1, main_point_left, main_point_right)
    print(f'max_f1={max_f1},max_f100values1={max_f100values1}')
    fault_dict['stator_winding_Loose'] = [max_f100values1,max_f100values2,max_f100values3]
    #2: stator bias:CF: RS * stator slot, band:LF; # 定子机械问题
    LFneg1_left,LFneg1_right,CF_left,CF_right,LFpos1_left,LFpos1_right = get_CF_LF(CF_stator_slot,LF,fs,N)
    max_f1,max_fLFneg1values1,max_value_loc1 = get_section_freq_max(real_amp1,freq_x1, LFneg1_left, LFneg1_right)
    max_f2,max_fLFneg1values2,max_value_loc2 = get_section_freq_max(real_amp2,freq_x1, LFneg1_left, LFneg1_right)
    max_f3,max_fLFneg1values3,max_value_loc3 = get_section_freq_max(real_amp3,freq_x1, LFneg1_left, LFneg1_right)

    max_f1,max_fCFvalues1,max_value_loc1 = get_section_freq_max(real_amp1,freq_x1, CF_left, CF_right)
    max_f2,max_fCFvalues2,max_value_loc2 = get_section_freq_max(real_amp2,freq_x1, CF_left, CF_right)
    max_f3,max_fCFvalues3,max_value_loc3 = get_section_freq_max(real_amp3,freq_x1, CF_left, CF_right)

    max_f1,max_fLFpos1values1,max_value_loc1 = get_section_freq_max(real_amp1,freq_x1, LFpos1_left, LFpos1_right)
    max_f2,max_fLFpos1values2,max_value_loc2 = get_section_freq_max(real_amp2,freq_x1, LFpos1_left, LFpos1_right)
    max_f3,max_fLFpos1values3,max_value_loc3 = get_section_freq_max(real_amp3,freq_x1, LFpos1_left, LFpos1_right)
    fault_dict['stator_bias'] = [max_fLFneg1values1,max_fLFneg1values2,max_fLFneg1values3,max_fCFvalues1,max_fCFvalues2,max_fCFvalues3,max_fLFpos1values1,max_fLFpos1values2,max_fLFpos1values3]
    # 3:定子电气问题-短路,LF =speed,
    # CF - LF, speed/60
    # CF + LF, speed/60
    LLFspeedneg1_left,LLFspeedneg1_right,CFLLF_left,CFLLF_right,LLFspeedpos1_left,LLFspeedpos1_right = get_CF_LF(CF_stator_slot - LF,speed/60,fs,N)
    RLFspeedneg1_left,RLFspeedneg1_right,CFRLF_left,CFRLF_right,RLFspeedpos1_left,RLFspeedpos1_right = get_CF_LF(CF_stator_slot + LF,speed/60,fs,N)

    max_f1,max_fLLFspeedneg1values1,max_value_loc1 = get_section_freq_max(real_amp1,freq_x1, LLFspeedneg1_left, LLFspeedneg1_right)
    max_f2,max_fLLFspeedneg1values2,max_value_loc2 = get_section_freq_max(real_amp2,freq_x1, LLFspeedneg1_left, LLFspeedneg1_right)
    max_f3,max_fLLFspeedneg1values3,max_value_loc3 = get_section_freq_max(real_amp3,freq_x1, LLFspeedneg1_left, LLFspeedneg1_right)
    # max_fLFneg1values1 max_fLFneg1values2 max_fLFneg1values3
    max_f1,max_fLLFspeedpos1values1,max_value_loc1 = get_section_freq_max(real_amp1,freq_x1, LLFspeedpos1_left, LLFspeedpos1_right)
    max_f2,max_fLLFspeedpos1values2,max_value_loc2 = get_section_freq_max(real_amp2,freq_x1, LLFspeedpos1_left, LLFspeedpos1_right)
    max_f3,max_fLLFspeedpos1values3,max_value_loc3 = get_section_freq_max(real_amp3,freq_x1, LLFspeedpos1_left, LLFspeedpos1_right)

    max_f1,max_fRLFspeedneg1values1,max_value_loc1 = get_section_freq_max(real_amp1,freq_x1, RLFspeedneg1_left, RLFspeedneg1_right)
    max_f2,max_fRLFspeedneg1values2,max_value_loc2 = get_section_freq_max(real_amp2,freq_x1, RLFspeedneg1_left, RLFspeedneg1_right)
    max_f3,max_fRLFspeedneg1values3,max_value_loc3 = get_section_freq_max(real_amp3,freq_x1, RLFspeedneg1_left, RLFspeedneg1_right)
    # max_fLFpos1values1 max_fLFpos1values2 max_fLFpos1values3
    max_f1,max_fRLFspeedpos1values1,max_value_loc1 = get_section_freq_max(real_amp1,freq_x1, RLFspeedpos1_left, RLFspeedpos1_right)
    max_f2,max_fRLFspeedpos1values2,max_value_loc2 = get_section_freq_max(real_amp2,freq_x1, RLFspeedpos1_left, RLFspeedpos1_right)
    max_f3,max_fRLFspeedpos1values3,max_value_loc3 = get_section_freq_max(real_amp3,freq_x1, RLFspeedpos1_left, RLFspeedpos1_right)


    fault_dict['stator_circuit'] = [max_fLLFspeedneg1values1,max_fLLFspeedneg1values2,max_fLLFspeedneg1values3,max_fLFneg1values1,max_fLFneg1values2,max_fLFneg1values3,max_fLLFspeedpos1values1,
                                    max_fLLFspeedpos1values2,max_fLLFspeedpos1values3,max_fRLFspeedneg1values1,max_fRLFspeedneg1values2,max_fRLFspeedneg1values3,max_fLFpos1values1,max_fLFpos1values2,
                                    max_fLFpos1values3,max_fRLFspeedpos1values1,max_fRLFspeedpos1values2,max_fRLFspeedpos1values3]
    
    # 4 转子偏心,RS*转子条数 2LF
    LFneg1_left,LFneg1_right,CF_left,CF_right,LFpos1_left,LFpos1_right = get_CF_LF(CF_rotor_slot,2*LF,fs,N)
    max_f1,max_fLFneg1values1,max_value_loc1 = get_section_freq_max(real_amp1,freq_x1, LFneg1_left, LFneg1_right)
    max_f2,max_fLFneg1values2,max_value_loc2 = get_section_freq_max(real_amp2,freq_x1, LFneg1_left, LFneg1_right)
    max_f3,max_fLFneg1values3,max_value_loc3 = get_section_freq_max(real_amp3,freq_x1, LFneg1_left, LFneg1_right)

    max_f1,max_fCFvalues1,max_value_loc1 = get_section_freq_max(real_amp1,freq_x1, CF_left, CF_right)
    max_f2,max_fCFvalues2,max_value_loc2 = get_section_freq_max(real_amp2,freq_x1, CF_left, CF_right)
    max_f3,max_fCFvalues3,max_value_loc3 = get_section_freq_max(real_amp3,freq_x1, CF_left, CF_right)

    max_f1,max_fLFpos1values1,max_value_loc1 = get_section_freq_max(real_amp1,freq_x1, LFpos1_left, LFpos1_right)
    max_f2,max_fLFpos1values2,max_value_loc2 = get_section_freq_max(real_amp2,freq_x1, LFpos1_left, LFpos1_right)
    max_f3,max_fLFpos1values3,max_value_loc3 = get_section_freq_max(real_amp3,freq_x1, LFpos1_left, LFpos1_right)
    fault_dict['rotor_bias'] = [max_fLFneg1values1,max_fLFneg1values2,max_fLFneg1values3,max_fCFvalues1,max_fCFvalues2,max_fCFvalues3,max_fLFpos1values1,max_fLFpos1values2,max_fLFpos1values3]

    # 5 转子断条:LF ,PPF
    LFneg1_left,LFneg1_right,CF_left,CF_right,LFpos1_left,LFpos1_right = get_CF_LF(LF,PPF,fs,N)
    max_f1,max_fLFneg1values1,max_value_loc1 = get_section_freq_max(real_amp1,freq_x1, LFneg1_left, LFneg1_right)
    max_f2,max_fLFneg1values2,max_value_loc2 = get_section_freq_max(real_amp2,freq_x1, LFneg1_left, LFneg1_right)
    max_f3,max_fLFneg1values3,max_value_loc3 = get_section_freq_max(real_amp3,freq_x1, LFneg1_left, LFneg1_right)

    max_f1,max_fCFvalues1,max_value_loc1 = get_section_freq_max(real_amp1,freq_x1, CF_left, CF_right)
    max_f2,max_fCFvalues2,max_value_loc2 = get_section_freq_max(real_amp2,freq_x1, CF_left, CF_right)
    max_f3,max_fCFvalues3,max_value_loc3 = get_section_freq_max(real_amp3,freq_x1, CF_left, CF_right)

    max_f1,max_fLFpos1values1,max_value_loc1 = get_section_freq_max(real_amp1,freq_x1, LFpos1_left, LFpos1_right)
    max_f2,max_fLFpos1values2,max_value_loc2 = get_section_freq_max(real_amp2,freq_x1, LFpos1_left, LFpos1_right)
    max_f3,max_fLFpos1values3,max_value_loc3 = get_section_freq_max(real_amp3,freq_x1, LFpos1_left, LFpos1_right)
    fault_dict['rotor_crack'] = [max_fLFneg1values1,max_fLFneg1values2,max_fLFneg1values3,max_fCFvalues1,max_fCFvalues2,max_fCFvalues3,max_fLFpos1values1,max_fLFpos1values2,max_fLFpos1values3]

    return fault_dict #

# stator short circuit:CF: RS * stator slot, band:LF; band RS;


if __name__ == '__main__':
    pass
    # # path_ref_wave_xyz = r'G:\12-1-21 - 副本.csv'
    # # path_ref_wave_xyz = r'G:\电流故障诊断\phase_current_1.csv'
    # path_ref_wave_x = r'G:\电流故障诊断\bias_A_fs5000.csv'
    # path_ref_wave_y = r'G:\电流故障诊断\bias_B_fs5000.csv'
    # path_ref_wave_z = r'G:\电流故障诊断\bias_C_fs5000.csv'
    # # norm_x = np.loadtxt(path_ref_wave_xyz)
    # # fs = 2560
    # fs = 5000
    # norm_x = np.loadtxt(path_ref_wave_x)
    # norm_y = np.loadtxt(path_ref_wave_y)
    # norm_z = np.loadtxt(path_ref_wave_z)
    # # 定子槽数:
    # stator_slot = 50
    # # 转子条数:
    # rotor_number = 36
    # # 转速
    # speed = 1458
    # # 级数
    # p = 4
    # CF_stator_slot = stator_slot * speed / 60
    # LF = 50
    # CF_rotor_slot = rotor_number * speed / 60
    # SS = 2* LF /p
    # S = SS - speed/60
    # PPF = S * p
    # print(f'CF_stator_slot={CF_stator_slot},CF_rotor_slot={CF_rotor_slot}')
    # fault_dict = electricity_fault(norm_x,norm_y,norm_z,fs,CF_stator_slot,CF_rotor_slot,LF,speed,PPF)
    # print(f'fault_dict={fault_dict}')



