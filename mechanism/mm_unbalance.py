'''
unbalance
'''
import numpy as np
import os
import sys
CURRENT_PATH = os.path.dirname(os.path.realpath(__file__))
sys.path.append(os.path.join(CURRENT_PATH, '..'))
from utils.scipy_fft import fft_plot
from utils.common_use import get_section_freq_max,get_max_fvalues_speednX_value # 返回3
from log.logger import LOG


from mechanism.mm_parent import MM_PARENT


class UNBALANCE(MM_PARENT):
    def __init__(self, args):
        super(UNBALANCE, self).__init__(args)

    # 机理模型诊断
    def diagnosis(self, wave_array):
        x = wave_array
        LOG.info(f'wave_array={wave_array}')

        fs = self.args.fs
        speed = self.args.speed
        LOG.info(f'fs={fs},speed={speed}')
        # model_unbalance_para = self.args.get('unbalance',-1)
        model_unbalance_para = self.args.model_fault_ratio 
        LOG.info(f'model_unbalance_para={model_unbalance_para}')
        if model_unbalance_para != -1:
            model_unbalance_para = np.linspace(model_unbalance_para,1,5).tolist()[:-1]  
        time_power = np.sum(np.power(x, 2))/fs
        # print(f'time_power={time_power}')
        unbalance = 0
        unbalance_level = 0
        speed_freq_possibility = 0
        N = len(x)
        deltf = fs / N
        speed_freq = speed/60
        speed_freq_left = speed_freq - 3
        speed_point_left = int(speed_freq_left * N / fs)
        speed_freq_right = speed_freq + 3
        speed_point_right = int(speed_freq_right * N / fs + 0.5)
        freq_x,real_amp,_angle = fft_plot(x, fs)
        if speed_point_left < 0:
            # print(f'error set left point,speed_point_left = {speed_point_left}, set to zero!')
            speed_point_left = 0
        if speed_point_left > len(real_amp) or speed_point_right > len(real_amp):
            LOG.info('!!!!!!转速设置超出采集频率范围:不诊断!!!!!!')
            return 0,0,0,0
        if speed_point_left == 0 and speed_point_right == 0:
            return 0,0,0,0
        max_value_loc = np.argmax(real_amp[speed_point_left:speed_point_right])
        # print(real_amp[speed_point_left:speed_point_right])
        judge_array = np.delete(real_amp[speed_point_left:speed_point_right],max_value_loc)
        up_line = np.mean(judge_array) + 3*np.std(judge_array)
        max_value_loc += speed_point_left
        max_fvalues = real_amp[max_value_loc]
        if max_fvalues > up_line:
            speed_1X = freq_x[max_value_loc]
            # print(f'修正转频:to max_f={speed_1X}')
        else:
            # print('使用额定转频')
            speed_1X = speed_freq
        max_fvalues_speed1X = max_fvalues.copy()
        # print(f'max_fvalues_speed1X={max_fvalues_speed1X}')
        unbalance_enery = (max_fvalues_speed1X /2 *N)**2/N/fs*2
        # print(f'unbalance_enery:{unbalance_enery}')
        unbalance_enery_ratio = unbalance_enery/time_power
        LOG.info(f'unbalance_enery_ratio={unbalance_enery_ratio}')
        if model_unbalance_para != -1:
            res = 0
            while res < len(model_unbalance_para) and unbalance_enery_ratio >= model_unbalance_para[res]:#max_fvalues_speed1X
                res += 1
            unbalance_level = res
            print(unbalance_enery_ratio, res,model_unbalance_para)
            if res == 0:
                unbalance = 0
            else:
                unbalance = 1
        else:
            unbalance = 0
            unbalance_level = 0
        # misalignment_enery = (max_fvalues_speed2X /2 *N)**2/N/fs*2 + unbalance_enery
        # speed_freq_values = max_fvalues_speed1X
        speed_freq_values = {'转频1X':max_fvalues_speed1X}
        if unbalance:
            speed_freq_possibility = 0.6 + 0.4/(1 - model_unbalance_para[0])*(unbalance_enery_ratio-model_unbalance_para[0]) #
        
        return unbalance_level,unbalance_enery_ratio,speed_freq_values,speed_freq_possibility
