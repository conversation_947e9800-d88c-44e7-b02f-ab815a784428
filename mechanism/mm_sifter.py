'''
振动筛:sifter.py
    激振器:exciter,
    model_fault_ratio
    model_fault_set_para
'''
import numpy as np
import pandas as pd
import os
import ast
import sys
CURRENT_PATH = os.path.dirname(os.path.realpath(__file__))
sys.path.append(os.path.join(CURRENT_PATH, '..'))
from mechanism.mm_bearing_sifter import SIFTERBEARING
import matplotlib
matplotlib.use('TkAgg')
import matplotlib.pyplot as plt
plt.rcParams['font.sans-serif']=['SimHei'] #用来正常显示中文标签
plt.rcParams['axes.unicode_minus']=False #用来正常显示负号
from log.logger import LOG


#激振器
class EXCITER(SIFTERBEARING):
    def __init__(self, args):
        super(EXCITER, self).__init__(args)
        """
        初始化
        vibration_data: 振动数据
        sampling_rate: 采样率
        """
        # self.data = np.array(vibration_data)
        # self.fs = sampling_rate
        
    def unbalance_detection(self):
        """
        不平衡故障检测
        特征：
        1. 基频（工作频率）幅值显著
        """
        # 计算频谱
        fft_data = np.fft.fft(self.data)
        freqs = np.fft.fftfreq(len(self.data), 1/self.fs)
        amplitude = np.abs(fft_data)
        
        # 寻找基频幅值
        working_freq_idx = np.argmax(amplitude[:len(amplitude)//2])
        working_freq_amp = amplitude[working_freq_idx]
        LOG.info(f'working_freq_amp={working_freq_amp}')
        # 简单的判断标准（需要根据实际情况调整）
        threshold = np.mean(amplitude) * 3
        return {
            "故障类型": "不平衡",
            "置信度": float(working_freq_amp > threshold),
            "特征频率": freqs[working_freq_idx]
        }
    
    def looseness_detection(self):
        """
        松动故障检测
        特征：
        1. 出现0.5X、1X、1.5X、2X等谐波
        2. 存在高次谐波
        """
        # 实现松动检测的代码
        loose_result = self.loose_diagnosis()
        print(f'loose_result={loose_result}')
        return loose_result
    
    def resonance_detection(self):
        """
        共振故障检测
        特征：
        1. 某个频率点幅值异常高
        2. 相位在共振点附近快速变化
        """
        # 实现共振检测的代码
        pass
    
    def bearing_fault_detection(self):
        """
        轴承故障检测
        """
        # 实现轴承故障检测的代码
        bearing_result = self.bearing_diagnosis()
        print(f'bearing_result={bearing_result}')
        return bearing_result
    
    def beam_crack(self):
        # 起吊梁开裂
        data1 = self.args.wave_array
        fs = self.args.fs
        N = len(data1)
        #---计算峭度--qd = pd.Series(x).kurt() + 3
        qd = pd.Series(data1).kurt() + 3
        model_bearing_para = self.args.model_fault_ratio 
        if model_bearing_para != -1:
            model_bearing_para = np.linspace(model_bearing_para,1000,5).tolist()[:-1]  
        
        LOG.info(f'model_bearing_para={model_bearing_para}, qd={qd}')
        crack_possibility = 0
        if model_bearing_para !=-1 :#
            res = 0
            while res < len(model_bearing_para) and qd >= model_bearing_para[res] :
                res += 1
            crack_level = res
        else:
            crack_level = 0
        if crack_level:
            crack_possibility = 0.6 + 0.4/(1-model_bearing_para[0])*(qd-model_bearing_para[0])
        return crack_level,qd,qd,crack_possibility
        


# 振动筛
    # model_fault_ratio
    # model_fault_set_para
class SIFTER:
    def __init__(self):
        self.fault_types = {
            "unbalance": "不平衡",
            "LOOSE": "松动",
            "resonance": "共振",
            "BEARING_FUZZY": "轴承故障",
            "screen_mesh": "筛网故障",
            "CRACK":"梁开裂"
        }
    
    @classmethod
    def diagnosis(cls, args):
        # 振动筛故障诊断
        fs = args.fs
        vibration_data = args.wave_array
        model_bearing_para = args.model_fault_ratio 

        threshold_middle_f4 = ast.literal_eval(args.model_fault_set_para).get('middle_freq_weight',-1)#0.05 10* 
        threshold_high_f4 = ast.literal_eval(args.model_fault_set_para).get('high_freq_weight',-1)# 0.6 1*
        LOG.info(f'in SIFTER threshold_middle_f4 = {threshold_middle_f4},threshold_high_f4={threshold_high_f4}')
        result = []
        
        # 创建诊断实例
        vfd = EXCITER(args) #SIFTERBEARING -- bearing_fuzzy --
        LOG.info(f'args.model_type={args.model_type}') 
        # 执行各类故障检测
        if args.model_type == 'BEARING_FUZZY':
            result = vfd.bearing_fault_detection()
        elif args.model_type == 'LOOSE':
            #looseness_detection
            result = vfd.looseness_detection()
        elif args.model_type == 'CRACK':
            result = vfd.beam_crack()


        LOG.info(f'result={result}')
        return result
    

    


if __name__ == '__main__':
    pass
    # path_wave = r'F:\data\新安振动数据导出\新安全厂20250121_20250128\test20250208\test\洗煤厂,四楼,319振动筛,激振器从动轴_319振动筛_激振器从动轴_8k 垂直加速度(0.5-2000)_1693534083541573634__1854814273495302145_\洗煤厂,四楼,319振动筛,激振器从动轴1693534083541573634_1854814273495302145_2025-01-21-01-46-26.csv'
    # wave_array = np.loadtxt(path_wave)
    # print(f'wave_array.shape={wave_array.shape}')
    # plt.plot(wave_array)
    # plt.show()
    # plt.title('test:fs:5120,N:8192')
    # print('end')
    # test = SIFTER()
    # print(test)
    # fs = 5120



