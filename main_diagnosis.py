import numpy as np
import traceback
import mechanism
import re

# from sys_log import logger
from log.logger import LOG
from sqlite_read.db.device_component_model_para_db import DeviceModelParaDao
from mechanism.mm_sifter import SIFTER
from mechanism.conveyor import CONVEYOR
from mechanism.centrifuge import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from mechanism.compressor import <PERSON><PERSON><PERSON><PERSON><PERSON>
from mechanism.screw_compressor import SCREW_COMPRESSOR


def convert_float64_to_float(obj):
    if isinstance(obj, np.float64):
        return round(float(obj), 3)
    elif isinstance(obj, dict):
        return {k: convert_float64_to_float(v) for k, v in obj.items()}
    elif isinstance(obj, list):
        return [convert_float64_to_float(item) for item in obj]
    else:
        return obj

# 在线诊断
def diagnosis(args):
    # 模型初始化 model_type：机理类型
    model = getattr(mechanism, args.model_type)(args)
    try:
        result = model.diagnosis(args.wave_array)
        print(f'result={result}')
        return result
    except Exception as e:
        LOG.info('在线训练异常信息：%s', e)
        LOG.info(traceback.print_exc())

# 在线诊断入口
def run(args):
    # 兼容新旧字段名
    model_list = getattr(args, 'model_ids', getattr(args, 'model_list', []))
    LOG.info(f'model_list={model_list}')
    LOG.info(f'args={args}')
    fault_type = []
    fault_grage = []
    fault_ratio = []
    fault_features = []
    fault_possibility = []
    for id in model_list:
        # TODO 根据ID获取模型参数model_type、model_fault_ratio、model_fault_set_para
        print(f'id = {id}')
        dao = DeviceModelParaDao()
        # dao.create()
        result = dao.query(where=' where id=?', args=(id,))
        print(f'result={result}')
        args.model_type = str.upper(result[0]['model_type'])
        args.model_fault_ratio = result[0]['model_fault_ratio']
        args.model_fault_set_para = result[0]['model_fault_set_para']
        args.device_type = result[0]['device_type']
        args.component_type = result[0]['component_type']
        LOG.info(f'args.device_type={args.device_type},args.component_type={args.component_type}')
        # if args.device_type == '振动筛' and args.component_type == '激振器' and args.model_type == 'SIFTERBEARING':
        if args.device_type == '振动筛' and args.component_type == '激振器' and args.model_type in ['BEARING_FUZZY','LOOSE','CRACK']:
            LOG.info(f'振动筛激振器轴承故障诊断:\n')
            result = SIFTER.diagnosis(args)
        elif args.device_type == '皮带机' and args.component_type in ['电机','减速机'] and args.model_type in ['RESONANCE']:
            LOG.info(f'皮带机故障诊断:\n')
            result = CONVEYOR.diagnosis(args)
        elif args.device_type == '离心机' and args.component_type in ['电机'] and args.model_type in ['CLOGGING']:
            LOG.info(f'离心机故障诊断:\n')
            result = CENTRIFUGE.diagnosis(args)
        elif args.device_type == '离心压缩机' and args.component_type in ['压缩机'] and args.model_type in ['SURGE']:
            LOG.info(f'离心压缩机故障诊断:\n')
            result = COMPRESSOR.diagnosis(args)
        elif args.device_type == '螺杆压缩机' and args.component_type in ['压缩机'] and args.model_type in ['WEAR']:
            LOG.info(f'螺杆压缩机故障诊断:\n')
            result = SCREW_COMPRESSOR.diagnosis(args)
        else: 
            result = diagnosis(args)
        fault_type.append(args.model_type)
        fault_grage.append(result[0])
        fault_ratio.append(result[1])
        fault_features.append(result[2])
        fault_possibility.append(result[3])

    #fault_ratio,
    fault_ratio = [round(float(item), 3) if isinstance(item, np.float64) else item for item in fault_ratio] #[float(i) for i in fault_ratio]
    #fault_features
    fault_features = [i if len(i) != 0 else {'None':None} for i in fault_features]
    fault_features = convert_float64_to_float(fault_features)
    #fault_possibility
    fault_possibility = [round(float(item), 3) if isinstance(item, np.float64) else None if item == [] else item for item in fault_possibility]

    # 返回字典格式
    return {
        "fault_type_list": fault_type,
        "fault_grade_list": fault_grage,
        "fault_ratio": fault_ratio,
        "fault_features": fault_features,
        "fault_possibility": fault_possibility
    }


class TEST():
    # path_wave = r'F:\data\新安振动数据导出\新安全厂20250121_20250128\test20250208\test\洗煤厂,四楼,319振动筛,激振器从动轴_319振动筛_激振器从动轴_8k 垂直加速度(0.5-2000)_1693534083541573634__1854814273495302145_\洗煤厂,四楼,319振动筛,激振器从动轴1693534083541573634_1854814273495302145_2025-01-21-01-46-26.csv'
    # path_wave = r'/Users/<USER>/data/mm/data/1693534535838539777_1790666557029560321_2024-07-26-04-51-40.csv'
    path_wave = '/Users/<USER>/sidas/data/171533154400011'
    # path_wave = r'G:\数据验证\入选皮带电机-轴承误报-导出数据\1693534535838539777_1790666557029560321\1693534535838539777_1790666557029560321_2024-07-26-04-51-40.csv'
    # path_wave = r'D:\文档\云科\机理模型平台\temp\1693534535838539777_1790666557029560321_2024-07-26-04-51-40.csv'
    industry = '洗煤厂'
    source = '新安洗煤厂'
    model_list = [1,2,3]#不平衡,不对中,松动,轴承内圈
    device_name ='319振动筛'
    device_type = '振动筛' #待定义int:1=合介泵,2=离心机,3=振动筛,...
    device_number='通用' # LX001
    device_power = 20
    device_speed = 1500
    component_name ='激振器主动轴' # 减速机一轴输入端
    component_type = '激振器' # 已定义:1=电机，2=风机，3=减速机，4=水泵，5=滚筒，6=激振器；7=
    bearing_parameter = [3.1, 4.1, 2.1, 0.5, 3.1, 4.1, 2.1, 0.5] # ---Sidas部位绑定的信息
    gearing_para = {'减速机一轴': [100.3, 123.3],'减速机二轴': [80.3, 88.3]} #---Sidas部位绑定的信息,[转速rpm,啮合频率]
    fan_number = [3] #---Sidas部位绑定的信息3 or 6
    component_speed = 1500 #存在优先使用
    measurement_definition_name = '8K加速度波形(0~2000)'
    fs = 5120
    N = 8192
    collect_time = '2024-06-22 10-58-24'
    wave_type = 1 #1: 加速度,速度,温度，应力波
    # wave_data = norm_x #波形数据
    # wave_array = np.loadtxt(path_wave) 

    try:
        # 打开文件
        with open(path_wave, 'r') as file:
            # 读取文件内容
            data = file.read()
        # 使用正则表达式找到所有浮点数
        float_regex = r"[-+]?\d*\.?\d+(?:[eE][-+]?\d+)?"        # 将列表转换为 NumPy 数组
        message_value = re.findall(float_regex, data)
        # 将科学计数法表示的字符串转换为浮点数，并去掉科学计数法
        float_data = [float('{:.20f}'.format(float(x))) for x in message_value]

        # 有效值判断
        # if float_data is not None and get_square(float_data) > 0.5:
        # 将一维数据重塑为二维
        wave_array = np.array(float_data)
        # wave_array = float_array.reshape(1, -1)
    except Exception as error:
        print("数据文件读取异常====== %s", error)
    
    speed = 1500
    

# if __name__ == '__main__':
#     print(f'TEST.path_wave={TEST.path_wave}')
#     print(f'TEST.path_wave={TEST.model_list}')
#     print(f'TEST.wave_array={TEST.wave_array}')
#     # model_list = [1]

#     result_dict = run(TEST)
#     print(f'fault_type={result_dict["fault_type_list"]},\n fault_grage={result_dict["fault_grade_list"]}')
#     print(f'fault_ratio={result_dict["fault_ratio"]},\n fault_features={result_dict["fault_features"]},\nfault_possibility={result_dict["fault_possibility"]}')
#     print('over')
