import sqlite3
import threading
from queue import Queue

from config.config_db import load_config_db_path
from config.config_env import env_name  # 导入环境名称变量

# 从配置文件加载数据库配置
db_path = load_config_db_path(env_name)

class SQLiteDB:
    _pool = None
    _lock = threading.Lock()

    @classmethod
    def get_pool(cls):
        if cls._pool is None:
            cls._lock.acquire()
            try:
                if cls._pool is None:
                    cls._pool = sqlite3.connect(db_path, check_same_thread=False)
            finally:
                cls._lock.release()
        return cls._pool

    def __init__(self):
        self.db_path = db_path
        self.pool = self.get_pool()
        self.queue = Queue()

    def _get_connection(self):
        return sqlite3.connect(self.db_path, check_same_thread=False)

    def _execute_query(self, query):
        connection = self.pool
        cursor = connection.cursor()
        cursor.execute(query)
        result = cursor.fetchall()
        cursor.close()
        connection.commit()  # 提交事务
        return result

    def execute_query(self, query):
        return self._execute_query(query)

    def execute_insert(self, table, columns, values):
        query = f"INSERT INTO {table} ({', '.join(columns)}) VALUES ({', '.join(['?'] * len(values))})"
        connection = self._get_connection()
        cursor = connection.cursor()
        cursor.execute(query, values)
        last_row_id = cursor.lastrowid  # 获取最后插入行的ID
        cursor.close()
        connection.commit()
        return last_row_id

    def execute_update(self, table, set_values, condition):
        set_clause = ', '.join([f"{column} = ?" for column in set_values.keys()])
        query = f"UPDATE {table} SET {set_clause} WHERE {condition}"
        values = list(set_values.values())
        connection = self._get_connection()
        cursor = connection.cursor()
        cursor.execute(query, values)
        cursor.close()
        connection.commit()
        return "Update successful."

    def execute_delete(self, table, condition):
        query = f"DELETE FROM {table} WHERE {condition}"
        connection = self._get_connection()
        cursor = connection.cursor()
        cursor.execute(query)
        cursor.close()
        connection.commit()
        return "Deletion successful."

# 使用示例
if __name__ == "__main__":
    db_path = "example.db"
    db = SQLiteDB(db_path)

    # 创建表格
    create_table_query = """
        CREATE TABLE IF NOT EXISTS students (
            id INTEGER PRIMARY KEY,
            name TEXT,
            age INTEGER
        )
    """
    db.execute_query(create_table_query)

    # 插入数据
    insert_data_query = "INSERT INTO students (name, age) VALUES (?, ?)"
    db.execute_insert("students", ["name", "age"], ["Alice", 20])

    # 更新数据
    update_data_query = "UPDATE students SET age = ? WHERE name = ?"
    db.execute_update("students", {"age": 22}, "name = 'Alice'")

    # 删除数据
    delete_data_query = "DELETE FROM students WHERE name = ?"
    db.execute_delete("students", "name = 'Bob'")

    # 查询数据
    select_data_query = "SELECT * FROM students"
    result = db.execute_query(select_data_query)
    print("All Students:")
    for row in result:
        print(row)
