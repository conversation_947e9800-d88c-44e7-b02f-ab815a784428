import threading
import time
import paho.mqtt.client as mqtt
import json
from utils.log import insert_diagnosis_log
from sys_log import logger


class MQTTSubscriber(threading.Thread):
    def __init__(self, host, port, username, password, client_id, topic, push_host, push_port, push_username, push_password, push_client_id, push_topic):
        super().__init__()
        self.host = host
        self.port = int(port)
        self.username = username
        self.password = password
        self.client_id = client_id
        self.topic = topic

        self.push_host = push_host
        self.push_port = int(push_port)
        self.push_username = push_username
        self.push_password = push_password
        self.push_client_id = push_client_id
        self.push_topic = push_topic

        self.client = mqtt.Client()
        self.client.username_pw_set(self.username, self.password)
        self.client.on_message = self._on_message
        self.should_stop = threading.Event()

    def connect(self):
        self.client.connect(self.host, self.port, keepalive=60)

    def run(self):
        try:
            self.connect()
            result, mid = self.client.subscribe(self.topic, qos=0)  # 订阅MQTT主题
            logger.info("机理模型设备诊断服务启动成功...：%s", result)
            if result == mqtt.MQTT_ERR_SUCCESS:
                logger.info("Successfully subscribed to topic: %s", self.topic)
            else:
                logger.error("Failed to subscribe to topic: %s, result: %d", self.topic, result)
            logger.info("机理模型设备诊断诊断服务启动成功...")
            while not self.should_stop.is_set():
                self.client.loop(timeout=1.0)  # 保持监听
        except Exception as e:
            logger.error("MQTT连接异常: %s", e)
            time.sleep(15)  # 重连前等待
            self.run()

    def stop_consuming(self):
        self.should_stop.set()
        self.client.disconnect()

    """
    设备推送信息接收处理：
    {
        "industry": "制造业", // 行业类型
        "source": "自动化系统", // 来源
        "device_name": "风机", // 设备名称
        "device_type": "离心风机", // 设备类型
        "device_num": "FJ-2023", // 设备型号
        "device_power": 1200, // 设备功率
        "device_speed": 1500, // 设备转速
        "position_name": "主轴", // 部位名称
        "position_type": "旋转部位", // 部位类型
        "bearing_num": "6312ZZ", // 轴承型号
        "gear_count": 4, // 齿轮数量
        "component_speed": 300, // 部件转速
        "sampling": 1024, // 采样点数
        "frequency": 2560, // 采样频率
        "origin_time": 1700000000, // 采样时间
        "model_name": "轴承故障诊断模型" // 模型名称
        }
    """
    def _on_message(self, client, userdata, msg):
        message = msg.payload.decode('utf-8')
        logger.info("Received message")

        try:
            dict_result = json.loads(message)
        except json.JSONDecodeError as e:
            logger.error("JSON decode error: %s", e)
            return

        # 将字符串转换为字典
        # iot_code
        industry = dict_result.get("industry")
        logger.info("Received industry: %s", industry)

        try:
            insert_diagnosis_log(
                industry="制造业",
                source="自动化系统",
                device_name="风机",
                device_type="离心风机",
                device_num="FJ-2023",
                device_power=1200,
                device_speed=1500,
                position_name="主轴",
                position_type="旋转部位",
                bearing_num="6312ZZ",
                gear_count=4,
                component_speed=300,
                sampling=1024,
                frequency=2560,
                origin_time=1700000000,
                model_name="轴承故障诊断模型"
            )
            # 推送字典
            result_dict = {
                            "device_name": "风机",
                            "device_type": "离心风机",
                            "device_num": "FJ-2023",
                            "result": '不平衡',
                            "resultCode": []
                        }
            # 消息推送
            push_thread = MQTTPusher(self.push_host, self.push_port, self.push_username, self.push_password, self.push_virtual_host, self.push_exchange, self.push_queue_name, self.push_routing_key, json.dumps(result_dict, ensure_ascii=False))
            push_thread.start()
        except Exception as error:
            logger.info("消息推送异常====== %s", error)


class MQTTPusher(threading.Thread):
    def __init__(self, host, port, username, password, client_id, topic, message):
        super().__init__()
        self.host = host
        self.port = int(port)
        self.username = username
        self.password = password
        self.client_id = client_id
        self.topic = topic
        self.message = message

        self.client = mqtt.Client()
        self.client.username_pw_set(self.username, self.password)

    def run(self):
        try:
            self.client.connect(self.host, self.port, keepalive=60)
            self.client.loop_start()
            self.client.publish(self.topic, self.message)  # 发布消息
            logger.info("Message published: %s", self.message)
        except Exception as error:
            logger.info("消息推送异常: %s", error)
        finally:
            self.client.loop_stop()
            self.client.disconnect()
